import{f as ke,a as Be,o as b,b as n,x as fe,a8 as ze,w as ee,y as ve,z as g,m as h,C as e,B as pe,D as De,F as he,M as Me,I as ne,J as te,L as $e,G as J,N as q,O as Re,P as Le,u as V,Q as O,K as se,T as Pe,a1 as He,a9 as Ue,ap as Ve}from"./legacy-YP6Kq8lu.js";import{l as We,p as ie,e as _,b as Se,i as be,f as _e,T as Te}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";import{h as j,W as we,e as je,i as qe}from"./host-BNehKqab.js";import{O as Je}from"./OpenFileButton-fKwL5bu0.js";import{S as Ke}from"./TextAreaAugment-Bs79KMH3.js";import{C as Qe}from"./check-B0ivqOmh.js";import{h as Xe}from"./IconButtonAugment-CbpcmeFk.js";import{C as Ee,E as Ae,D as W,R as Ye,M as ge,c as Ze,d as et,e as tt}from"./index-vTFXV1kt.js";import{M as ye}from"./message-broker-DRrss2z_.js";import{M as st}from"./MarkdownEditor-DW883vmz.js";import{B as Fe}from"./ButtonAugment-DkEdzEZO.js";import{C as Ge}from"./chevron-down-B0l__RXq.js";import{F as ot}from"./Filespan-Dfz0pJHr.js";import{T as Ie,a as re}from"./CardAugment-D6yVifBE.js";import"./chat-model-context-DZ2DTs5O.js";import"./index-B528snJk.js";import"./chat-types-BfwvR7Kn.js";import"./index-4vhrZf9p.js";import"./remote-agents-client-DbhVjGoZ.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-BNum2ZUy.js";import"./input-C2nR_fsN.js";import"./BaseTextInput-Br9yLRnx.js";import"./async-messaging-gS_K9w3p.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-BNqj-rl6.js";import"./lodash-DfmeyYaq.js";import"./event-modifiers-Bz4QCcZc.js";var at=ke("<svg><!></svg>");function xe(K,z){const $=We(z,["children","$$slots","$$events","$$legacy"]);var S=at();Be(S,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...$}));var i=b(S);Xe(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),n(K,S)}var nt=J("<!> <!>",1),rt=J('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),it=J("<!> <!>",1);function lt(K,z){fe(z,!1);const[$,S]=Se(),i=()=>_(s,"$rulesFiles",$),k=()=>_(L,"$selectedRule",$),w=()=>_(e(F),"$focusedIndex",$),R=h(),Q=h(),X=h();let oe=ie(z,"onRuleSelected",8),ae=ie(z,"disabled",8,!1);const le=new ye(j),ce=new Ee,l=new Ae(j,le,ce),s=ee([]),y=ee(!0),L=ee(void 0);let F=h(void 0),B=h(()=>{});ze(()=>{(async function(){try{y.set(!0);const u=await l.findRules("",100);s.set(u)}catch(u){console.error("Failed to load rules:",u),s.set([])}finally{y.set(!1)}})();const d=u=>{var T;((T=u.data)==null?void 0:T.type)===we.getRulesListResponse&&(s.set(u.data.data||[]),y.set(!1))};return window.addEventListener("message",d),()=>{window.removeEventListener("message",d)}});let D=h(),x=h(!1);function de(d){g(x,d)}ve(()=>i(),()=>{g(R,i().length>0)}),ve(()=>(pe(ae()),e(R)),()=>{g(Q,ae()||!e(R))}),ve(()=>e(R),()=>{g(X,e(R)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),De(),he();var c=Me(),Y=ne(c),Z=d=>{var u=Me(),T=ne(u),ue=m=>{W.Root(m,{onOpenChange:de,get requestClose(){return e(B)},set requestClose(a){g(B,a)},get focusedIndex(){return e(F)},set focusedIndex(a){_e(g(F,a),"$focusedIndex",$)},children:(a,v)=>{var f=it(),p=ne(f);W.Trigger(p,{children:(E,me)=>{const H=O(()=>(pe(re),V(()=>[re.Hover]))),U=O(()=>!e(x)&&void 0);be(Ie(E,{get content(){return e(X)},get triggerOn(){return e(H)},side:"top",get open(){return e(U)},children:(A,C)=>{Fe(A,{color:"neutral",variant:"soft",size:1,get disabled(){return e(Q)},children:(r,o)=>{var M=q();Re(()=>Le(M,(k(),V(()=>k()?k().path:"Rules")))),n(r,M)},$$slots:{default:!0,iconLeft:(r,o)=>{xe(r,{slot:"iconLeft"})},iconRight:(r,o)=>{Ge(r,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),A=>g(D,A),()=>e(D))},$$slots:{default:!0}});var P=se(p,2);W.Content(P,{side:"bottom",align:"start",children:(E,me)=>{var H=rt(),U=b(H);je(U,1,i,qe,(r,o,M)=>{const N=O(()=>w()===M);W.Item(r,{onSelect:()=>function(G){L.set(G),oe()(G),e(B)()}(e(o)),get highlight(){return e(N)},children:(G,I)=>{ot(G,{get filepath(){return e(o),V(()=>e(o).path)}})},$$slots:{default:!0}})});var A=se(U,2),C=r=>{var o=nt(),M=ne(o);W.Separator(M,{});var N=se(M,2);W.Label(N,{children:(G,I)=>{Te(G,{size:1,color:"neutral",children:(Ne,mt)=>{var Ce=q();Re(Oe=>Le(Ce,Oe),[()=>(i(),w(),V(()=>`Move to ${i()[w()].path}`))],O),n(Ne,Ce)},$$slots:{default:!0}})},$$slots:{default:!0}}),n(r,o)};te(A,r=>{w(),i(),V(()=>w()!==void 0&&i()[w()])&&r(C)}),n(E,H)},$$slots:{default:!0}}),n(a,f)},$$slots:{default:!0},$$legacy:!0})},t=m=>{const a=O(()=>(pe(re),V(()=>[re.Hover])));be(Ie(m,{get content(){return e(X)},get triggerOn(){return e(a)},side:"top",children:(v,f)=>{Fe(v,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(p,P)=>{var E=q("Rules");n(p,E)},$$slots:{default:!0,iconLeft:(p,P)=>{xe(p,{slot:"iconLeft"})},iconRight:(p,P)=>{Ge(p,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),v=>g(D,v),()=>e(D))};te(T,m=>{e(R)?m(ue):m(t,!1)}),n(d,u)};te(Y,d=>{_(y,"$loading",$)||d(Z)}),n(K,c),$e(),S()}var ct=J('<div slot="iconLeft" class="c-move-text-btn__left_icon svelte-1yddhs6"><!></div>'),dt=J('<div class="l-file-controls svelte-1yddhs6" slot="header"><div class="l-file-controls-left svelte-1yddhs6"><div class="c-move-text-btn svelte-1yddhs6"><!></div> <div class="c-move-text-btn svelte-1yddhs6"><!></div></div> <div class="l-file-controls-right svelte-1yddhs6"><!></div></div>'),ut=J('<div class="c-memories-container svelte-1vchs21"><!></div>');Ve(function(K,z){fe(z,!1);const[$,S]=Se(),i=()=>_(R,"$editorContent",$),k=()=>_(Q,"$editorPath",$),w=new ye(j),R=ee(null),Q=ee(null),X={handleMessageFromExtension(l){const s=l.data;if(s&&s.type===we.loadFile){if(s.data.content!==void 0){const y=s.data.content.replace(/^\n+/,"");R.set(y)}s.data.pathName&&Q.set(s.data.pathName)}return!0}};ze(()=>{w.registerConsumer(X),j.postMessage({type:we.memoriesLoaded})}),Pe(()=>{w.dispose()}),he();var oe=ut();He("message",Ue,function(...l){var s;(s=w.onMessageFromExtension)==null||s.apply(this,l)});var ae=b(oe),le=l=>{(function(s,y){fe(y,!1);let L=ie(y,"text",12),F=ie(y,"path",8);const B=new ye(j),D=new Ee,x=new Ae(j,B,D),de=new Ye(B);let c=h(""),Y=h(0),Z=h(0),d=h("neutral");const u=async()=>{F()&&x.saveFile({repoRoot:"",pathName:F(),content:L()})};async function T(t){if(!e(c))return;let m,a,v;const f=e(c).slice(0,20);if(t==="userGuidelines"?(m="Move Content to User Guidelines",a=`Are you sure you want to move the selected content "${f}" to your user guidelines?`,v=ge.userGuidelines):t==="augmentGuidelines"?(m="Move Content to Workspace Guidelines",a=`Are you sure you want to move the selected content "${f}" to workspace guidelines?`,v=ge.augmentGuidelines):(m="Move Content to Rule",a=`Are you sure you want to move the selected content "${f}" to rule file "${t.rule.path}"?`,v=ge.rules),!await x.openConfirmationModal({title:m,message:a,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?x.updateUserGuidelines(e(c)):t==="augmentGuidelines"?x.updateWorkspaceGuidelines(e(c)):(await de.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(c),description:t.rule.description}),x.showNotification({message:`Moved content "${f}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${et}/${tt}/${t.rule.path}`}}));const p=L().substring(0,e(Y))+L().substring(e(Z));return L(p),await u(),x.reportAgentSessionEvent({eventName:Ze.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:v}}}),"success"}async function ue(t){await T({rule:t})}he(),st(s,{saveFunction:u,variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get selectedText(){return e(c)},set selectedText(t){g(c,t)},get selectionStart(){return e(Y)},set selectionStart(t){g(Y,t)},get selectionEnd(){return e(Z)},set selectionEnd(t){g(Z,t)},get value(){return L()},set value(t){L(t)},$$slots:{header:(t,m)=>{var a=dt(),v=b(a),f=b(v),p=b(f);const P=O(()=>!e(c));Ke(p,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>T("userGuidelines"),get disabled(){return e(P)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(d)},set state(C){g(d,C)},children:(C,r)=>{var o=q("User Guidelines");n(C,o)},$$slots:{default:!0,iconLeft:(C,r)=>{var o=ct(),M=b(o),N=I=>{Qe(I,{})},G=I=>{xe(I,{})};te(M,I=>{e(d)==="success"?I(N):I(G,!1)}),n(C,o)}},$$legacy:!0});var E=se(f,2),me=b(E);const H=O(()=>!e(c));lt(me,{onRuleSelected:ue,get disabled(){return e(H)}});var U=se(v,2),A=b(U);Je(A,{size:1,get path(){return F()},variant:"soft",onOpenLocalFile:async()=>(x.openFile({repoRoot:"",pathName:F()}),"success"),$$slots:{text:(C,r)=>{Te(C,{slot:"text",size:1,children:(o,M)=>{var N=q("Augment-Memories.md");n(o,N)},$$slots:{default:!0}})}}}),n(t,a)}},$$legacy:!0}),$e()})(l,{get text(){return i()},get path(){return k()}})},ce=l=>{var s=q("Loading memories...");n(l,s)};te(ae,l=>{i()!==null&&k()!==null?l(le):l(ce,!1)}),n(K,oe),$e(),S()},{target:document.getElementById("app")});
