<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Memories Editor</title>
    <meta property="csp-nonce" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <script type="module" crossorigin src="./assets/memories-YF5XUiea.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/legacy-YP6Kq8lu.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-Dpcl1cXc.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BkqeNcXX.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/host-BNehKqab.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CbpcmeFk.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/event-modifiers-Bz4QCcZc.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-D6yVifBE.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-DZ2DTs5O.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-BfwvR7Kn.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-gS_K9w3p.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CaEmYw0i.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BNqj-rl6.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/input-C2nR_fsN.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-Br9yLRnx.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-vTFXV1kt.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-4vhrZf9p.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DbhVjGoZ.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-BNum2ZUy.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DkEdzEZO.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-Bs79KMH3.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-fKwL5bu0.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/check-B0ivqOmh.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DRrss2z_.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-DfmeyYaq.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/MarkdownEditor-DW883vmz.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-B0l__RXq.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-Dfz0pJHr.js" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/MarkdownEditor-B6vv3aGc.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-C754ShtV.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
    <link rel="stylesheet" crossorigin href="./assets/memories-DTEUFlBS.css" nonce="nonce-dPmNF8lC4QnNXPkmq4fAgQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
