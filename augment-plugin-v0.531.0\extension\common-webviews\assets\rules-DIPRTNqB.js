import{x as Q,m as b,y as L,z as f,B as N,C as e,D as ne,F as U,G as M,o as g,K as F,N as D,b as n,M as ce,I as pe,J as W,u as de,L as J,a8 as me,a1 as ue,a9 as ve,w as fe,ap as ge}from"./legacy-YP6Kq8lu.js";import{p as he,T as S,b as $e,e as ye}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";import{h as w,W as B}from"./host-BNehKqab.js";import{B as be}from"./ButtonAugment-DkEdzEZO.js";import{O as we}from"./OpenFileButton-fKwL5bu0.js";import{C as ze,R as Ee,E as Re,T as Fe,a as G}from"./index-vTFXV1kt.js";import{M as X}from"./message-broker-DRrss2z_.js";import{M as Me}from"./MarkdownEditor-DW883vmz.js";import{R as Y}from"./chat-types-BfwvR7Kn.js";import{R as Ce}from"./RulesModeSelector-WuYQ8PVD.js";import{C as Te}from"./chevron-left-COQlfEVN.js";import{T as ke}from"./CardAugment-D6yVifBE.js";import{l as xe}from"./lodash-DfmeyYaq.js";import"./IconButtonAugment-CbpcmeFk.js";import"./chat-model-context-DZ2DTs5O.js";import"./index-B528snJk.js";import"./index-4vhrZf9p.js";import"./remote-agents-client-DbhVjGoZ.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-BNum2ZUy.js";import"./TextAreaAugment-Bs79KMH3.js";import"./input-C2nR_fsN.js";import"./BaseTextInput-Br9yLRnx.js";import"./async-messaging-gS_K9w3p.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-BNqj-rl6.js";import"./chevron-down-B0l__RXq.js";import"./event-modifiers-Bz4QCcZc.js";var Le=M('<div class="c-rule-config svelte-1r8al3d"><div class="c-rule-field c-rule-field-full-width svelte-1r8al3d"><!> <!></div></div>'),Ne=M('<div class="l-file-controls svelte-1r8al3d" slot="header"><div class="l-file-controls-left svelte-1r8al3d"><div class="c-trigger-section svelte-1r8al3d"><!> <!> <!></div></div> <!></div>'),De=M("<div>Loading...</div>"),Se=M('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ge(function(Z,_){Q(_,!1);const[q,H]=$e(),O=()=>ye(I,"$rule",q),A=new X(w),I=fe(null),V={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===B.loadFile&&t){const h=t.data.content;if(h!==void 0){const a=h.replace(/^\n+/,""),o=G.getDescriptionFrontmatterKey(a),z=G.getRuleTypeFromContent(a),r=G.extractContent(a);I.set({path:t.data.pathName,content:r,type:z,description:o})}}return!0}};me(()=>{A.registerConsumer(V),w.postMessage({type:B.rulesLoaded})}),U();var K=Se();ue("message",ve,function(...s){var t;(t=A.onMessageFromExtension)==null||t.apply(this,s)});var j=g(K),ee=s=>{(function(t,h){Q(h,!1);const a=b(),o=b(),z=b();let r=he(h,"rule",12),$=b(r().content),i=b(r().description);const P=new X(w),se=new ze,ae=new Re(w,P,se),re=new Ee(P),C=async(l,d)=>{r({...r(),type:l,description:d||e(i)}),d!==void 0&&f(i,d);try{await re.updateRuleContent({type:l,path:e(a),content:e($),description:d||e(i)})}catch(c){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",c)}},oe=xe.debounce(C,500),ie=()=>{w.postMessage({type:B.openSettingsPage,data:"guidelines"})};L(()=>N(r()),()=>{f(a,r().path)}),L(()=>N(r()),()=>{f(o,r().type)}),L(()=>(e(a),e(o),e($),e(i)),()=>{f(z,{path:e(a),type:e(o),content:e($),description:e(i)})}),ne(),U(),Me(t,{saveFunction:()=>C(e(o),e(i)),variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get value(){return e($)},set value(l){f($,l)},children:(l,d)=>{var c=ce(),E=pe(c),T=m=>{var y=Le(),k=g(y),R=g(k);S(R,{size:1,class:"c-field-label",children:(u,v)=>{var x=D("Description");n(u,x)},$$slots:{default:!0}});var p=F(R,2);Fe(p,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(i)},set value(u){f(i,u)},$$events:{input:()=>oe(e(o),e(i))},$$legacy:!0}),n(m,y)};W(E,m=>{e(o),N(Y),de(()=>e(o)===Y.AGENT_REQUESTED)&&m(T)}),n(l,c)},$$slots:{default:!0,header:(l,d)=>{var c=Ne(),E=g(c),T=g(E),m=g(T);ke(m,{content:"Navigate back to all Rules & Guidelines",children:(p,u)=>{be(p,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(v,x)=>{Te(v,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var y=F(m,2);S(y,{size:1,class:"c-field-label",children:(p,u)=>{var v=D("Trigger:");n(p,v)},$$slots:{default:!0}});var k=F(y,2);Ce(k,{onSave:C,get rule(){return e(z)}});var R=F(E,2);we(R,{size:1,get path(){return e(a)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(a)}),"success"),$$slots:{text:(p,u)=>{S(p,{slot:"text",size:1,children:(v,x)=>{var le=D("Open file");n(v,le)},$$slots:{default:!0}})}}}),n(l,c)}},$$legacy:!0}),J()})(s,{get rule(){return O()}})},te=s=>{var t=De();n(s,t)};W(j,s=>{O()!==null?s(ee):s(te,!1)}),n(Z,K),J(),H()},{target:document.getElementById("app")});
