import{O as A,a6 as V,aL as W,aM as X,a4 as Z,aN as w,c as aa,E as ea,l as sa,aa as I,a2 as ta,a0 as na,u as F,v as la,B as y,aJ as ia,x as D,y as ra,D as ca,F as oa,G as J,a as ua,C as p,m as M,H as da,J as ha,o as x,a1 as f,b as _,L as G,I as B,Q as va,K as ga,M as L,z as S,A as ba,am as fa,Y as $a}from"./legacy-YP6Kq8lu.js";import{b as e}from"./host-BNehKqab.js";import{l as j,p as i,d as ma,j as ya,S as pa,a as q,h as H,r as K}from"./SpinnerAugment-Dpcl1cXc.js";function Fa(d,a,r=!1,t=!1,u=!1){var o=d,c="";A(()=>{var b=V;if(c!==(c=a()??"")&&(b.nodes_start!==null&&(W(b.nodes_start,b.nodes_end),b.nodes_start=b.nodes_end=null),c!=="")){var h=c+"";r?h=`<svg>${h}</svg>`:t&&(h=`<math>${h}</math>`);var v=X(h);if((r||t)&&(v=w(v)),Z(w(v),v.lastChild),r||t)for(;w(v);)o.before(w(v));else o.before(v)}})}function N(d,a,...r){var t,u=d,o=I;aa(()=>{o!==(o=a())&&(t&&(ta(t),t=null),t=sa(()=>o(u,...r)))},ea)}function Ia(d,a,r){na(()=>{var t=F(()=>a(d,r==null?void 0:r())||{});if(r&&(t!=null&&t.update)){var u=!1,o={};la(()=>{var c=r();y(c),u&&ia(o,c)&&(o=c,t.update(c))}),u=!0}if(t!=null&&t.destroy)return()=>t.destroy()})}function O(d){return String(d).replace(".","_")}var za=J('<div class="c-base-btn__loading svelte-5auyf2"><!></div> <span class="c-base-btn__hidden-content svelte-5auyf2"><!></span>',1),Ca=J("<button><!></button>");function _a(d,a){const r=j(a,["children","$$slots","$$events","$$legacy"]),t=j(r,["size","variant","color","disabled","highContrast","loading","alignment","radius"]);D(a,!1);const u=M(),o=M();let c=i(a,"size",8,2),b=i(a,"variant",8,"solid"),h=i(a,"color",8,"accent"),v=i(a,"disabled",8,!1),n=i(a,"highContrast",8,!1),z=i(a,"loading",8,!1),$=i(a,"alignment",8,"center"),l=i(a,"radius",8,"medium");ra(()=>(p(u),p(o),y(t)),()=>{S(u,t.class),S(o,ba(t,["class"]))}),ca(),oa();var g=Ca();ua(g,(s,m,C,E)=>({...s,...m,class:C,disabled:v()||z(),...p(o),[da]:E}),[()=>ma(h()),()=>ya(l()),()=>(y(c()),y(b()),y(h()),p(u),y($()),F(()=>`c-base-btn c-base-btn--size-${O(c())} c-base-btn--${b()} c-base-btn--${h()} ${p(u)} c-base-btn--alignment-${$()}`)),()=>({"c-base-btn--highContrast":n(),"c-base-btn--loading":z()})],"svelte-5auyf2");var k=x(g),Q=s=>{var m=za(),C=B(m),E=x(C);const P=va(()=>(y(c()),F(()=>function(U){switch(U){case 0:case .5:case 1:return 1;case 2:case 3:return 2;case 4:return 3}}(c()))));pa(E,{get size(){return p(P)}});var R=ga(C,2),T=x(R);q(T,a,"default",{},null),_(s,m)},Y=s=>{var m=L(),C=B(m);q(C,a,"default",{},null),_(s,m)};ha(k,s=>{z()?s(Q):s(Y,!1)}),f("click",g,function(s){e.call(this,a,s)}),f("keyup",g,function(s){e.call(this,a,s)}),f("keydown",g,function(s){e.call(this,a,s)}),f("mousedown",g,function(s){e.call(this,a,s)}),f("mouseover",g,function(s){e.call(this,a,s)}),f("focus",g,function(s){e.call(this,a,s)}),f("mouseleave",g,function(s){e.call(this,a,s)}),f("blur",g,function(s){e.call(this,a,s)}),f("contextmenu",g,function(s){e.call(this,a,s)}),_(d,g),G()}var ka=J("<div><!></div>");function wa(d,a){D(a,!0);let r=i(a,"size",3,2),t=i(a,"variant",3,"solid"),u=i(a,"color",3,"accent"),o=i(a,"highContrast",3,!1),c=i(a,"disabled",3,!1),b=i(a,"radius",3,"medium"),h=i(a,"loading",3,!1),v=K(a,["$$slots","$$events","$$legacy","size","variant","color","highContrast","disabled","radius","class","loading","children"]);var n=ka(),z=x(n);const $=fa(()=>c()||void 0);_a(z,H({get size(){return r()},get variant(){return t()},get color(){return u()},get highContrast(){return o()},get disabled(){return p($)},get radius(){return b()},get loading(){return h()},get class(){return a.class}},()=>v,{$$events:{click(l){e.call(this,a,l)},keyup(l){e.call(this,a,l)},keydown(l){e.call(this,a,l)},mousedown(l){e.call(this,a,l)},mouseover(l){e.call(this,a,l)},focus(l){e.call(this,a,l)},mouseleave(l){e.call(this,a,l)},blur(l){e.call(this,a,l)},contextmenu(l){e.call(this,a,l)}},children:(l,g)=>{var k=L();N(B(k),()=>a.children??I),_(l,k)},$$slots:{default:!0}})),A(l=>$a(n,1,l,"svelte-1mz435m"),[()=>`c-icon-btn c-icon-btn--size-${O(r())}`]),_(d,n),G()}function Ja(d,a){let r=i(a,"size",3,2),t=i(a,"variant",3,"solid"),u=i(a,"color",3,"neutral"),o=i(a,"highContrast",3,!1),c=i(a,"disabled",3,!1),b=i(a,"radius",3,"medium"),h=i(a,"loading",3,!1),v=K(a,["$$slots","$$events","$$legacy","size","variant","color","highContrast","disabled","radius","loading","children"]);wa(d,H({get size(){return r()},get variant(){return t()},get color(){return u()},get highContrast(){return o()},get disabled(){return c()},get radius(){return b()},get loading(){return h()}},()=>v,{$$events:{click(n){e.call(this,a,n)},keyup(n){e.call(this,a,n)},keydown(n){e.call(this,a,n)},mousedown(n){e.call(this,a,n)},mouseover(n){e.call(this,a,n)},focus(n){e.call(this,a,n)},mouseleave(n){e.call(this,a,n)},blur(n){e.call(this,a,n)},contextmenu(n){e.call(this,a,n)}},children:(n,z)=>{var $=L();N(B($),()=>a.children??I),_(n,$)},$$slots:{default:!0}}))}export{_a as B,wa as C,Ja as I,Ia as a,Fa as h,N as s};
