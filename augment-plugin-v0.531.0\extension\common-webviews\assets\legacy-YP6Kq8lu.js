var Es=Object.defineProperty;var ks=(t,e,n)=>e in t?Es(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var ke=(t,e,n)=>ks(t,typeof e!="symbol"?e+"":e,n);(function(){const t=document.createElement("link").relList;if(!(t&&t.supports&&t.supports("modulepreload"))){for(const n of document.querySelectorAll('link[rel="modulepreload"]'))e(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const s of r.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&e(s)}).observe(document,{childList:!0,subtree:!0})}function e(n){if(n.ep)return;n.ep=!0;const r=function(s){const a={};return s.integrity&&(a.integrity=s.integrity),s.referrerPolicy&&(a.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?a.credentials="include":s.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}(n);fetch(n.href,r)}})();const A=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ct="9.20.0",q=globalThis;function Et(){return he(q),q}function he(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||Ct,e[Ct]=e[Ct]||{}}function Zt(t,e,n=q){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[Ct]=r[Ct]||{};return s[t]||(s[t]=e())}const Zn=Object.prototype.toString;function Is(t){switch(Zn.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return qt(t,Error)}}function kt(t,e){return Zn.call(t)===`[object ${e}]`}function Ga(t){return kt(t,"ErrorEvent")}function Ha(t){return kt(t,"DOMError")}function Qa(t){return kt(t,"DOMException")}function te(t){return kt(t,"String")}function As(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function Xa(t){return t===null||As(t)||typeof t!="object"&&typeof t!="function"}function tr(t){return kt(t,"Object")}function Os(t){return typeof Event<"u"&&qt(t,Event)}function er(t){return!!(t!=null&&t.then&&typeof t.then=="function")}function qt(t,e){try{return t instanceof e}catch{return!1}}function nr(t){return!(typeof t!="object"||t===null||!t.__isVue&&!t._isVue)}function Za(t){return typeof Request<"u"&&qt(t,Request)}const Ke=q,$s=80;function Ts(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let a=0,o=0;const c=" > ",i=c.length;let u;const l=Array.isArray(e)?e:e.keyAttrs,f=!Array.isArray(e)&&e.maxStringLength||$s;for(;n&&a++<r&&(u=Ns(n,l),!(u==="html"||a>1&&o+s.length*i+u.length>=f));)s.push(u),o+=u.length,n=n.parentNode;return s.reverse().join(c)}catch{return"<unknown>"}}function Ns(t,e){const n=t,r=[];if(!(n!=null&&n.tagName))return"";if(Ke.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e!=null&&e.length?e.filter(o=>n.getAttribute(o)).map(o=>[o,n.getAttribute(o)]):null;if(s!=null&&s.length)s.forEach(o=>{r.push(`[${o[0]}="${o[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const o=n.className;if(o&&te(o)){const c=o.split(/\s+/);for(const i of c)r.push(`.${i}`)}}const a=["aria-label","type","name","title","alt"];for(const o of a){const c=n.getAttribute(o);c&&r.push(`[${o}="${c}"]`)}return r.join("")}function to(){try{return Ke.document.location.href}catch{return""}}function eo(t){if(!Ke.HTMLElement)return null;let e=t;for(let n=0;n<5;n++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}const gn=["debug","info","warn","error","log","assert","trace"],yn={};function Ye(t){if(!("console"in q))return t();const e=q.console,n={},r=Object.keys(yn);r.forEach(s=>{const a=yn[s];n[s]=e[s],e[s]=a});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}const x=Zt("logger",function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return A?gn.forEach(n=>{e[n]=(...r)=>{t&&Ye(()=>{q.console[n](`Sentry Logger [${n}]:`,...r)})}}):gn.forEach(n=>{e[n]=()=>{}}),e});function je(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function no(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const s=t[r];try{nr(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function Cs(t,e,n=!1){return!!te(t)&&(kt(e,"RegExp")?e.test(t):!!te(e)&&(n?t===e:t.includes(e)))}function ro(t,e=[],n=!1){return e.some(r=>Cs(t,r,n))}function so(t,e,n){if(!(e in t))return;const r=t[e];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&js(s,r);try{t[e]=s}catch{A&&x.log(`Failed to replace method "${e}" in object`,t)}}function tt(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{A&&x.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function js(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,tt(t,"__sentry_original__",e)}catch{}}function ao(t){return t.__sentry_original__}function rr(t){if(Is(t))return{message:t.message,name:t.name,stack:t.stack,...Sn(t)};if(Os(t)){const e={type:t.type,target:bn(t.target),currentTarget:bn(t.currentTarget),...Sn(t)};return typeof CustomEvent<"u"&&qt(t,CustomEvent)&&(e.detail=t.detail),e}return t}function bn(t){try{return e=t,typeof Element<"u"&&qt(e,Element)?Ts(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}var e}function Sn(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function oo(t,e=40){const n=Object.keys(rr(t));n.sort();const r=n[0];if(!r)return"[object has no keys]";if(r.length>=e)return je(r,e);for(let s=n.length;s>0;s--){const a=n.slice(0,s).join(", ");if(!(a.length>e))return s===n.length?a:je(a,e)}return""}function it(t=function(){const e=q;return e.crypto||e.msCrypto}()){let e=()=>16*Math.random();try{if(t!=null&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t!=null&&t.getRandomValues&&(e=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&e())>>n/4).toString(16))}function sr(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}function io(t){const{message:e,event_id:n}=t;if(e)return e;const r=sr(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function co(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],a=s[0]=s[0]||{};a.value||(a.value=e||""),a.type||(a.type="Error")}function uo(t,e){const n=sr(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const s={...r==null?void 0:r.data,...e.data};n.mechanism.data=s}}function lo(t){if(function(e){try{return e.__sentry_captured__}catch{}}(t))return!0;try{tt(t,"__sentry_captured__",!0)}catch{}return!1}const ar=1e3;function or(){return Date.now()/ar}const Dt=function(){const{performance:t}=q;if(!(t!=null&&t.now))return or;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/ar}();let Ie;function fo(){return Ie||(Ie=function(){var i;const{performance:t}=q;if(!(t!=null&&t.now))return[void 0,"none"];const e=36e5,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,a=s<e,o=(i=t.timing)==null?void 0:i.navigationStart,c=typeof o=="number"?Math.abs(o+n-r):e;return a||c<e?s<=c?[t.timeOrigin,"timeOrigin"]:[o,"navigationStart"]:[r,"dateNow"]}()),Ie[0]}function po(t){const e=Dt(),n={sid:it(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return t&&Ge(n,t),n}function Ge(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||Dt(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:it()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function ho(t,e){let n={};t.status==="ok"&&(n={status:"exited"}),Ge(t,n)}function ir(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&Object.keys(e).length===0)return t;const r={...t};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=ir(r[s],e[s],n-1));return r}const Pe="_sentrySpan";function ee(t,e){e?tt(t,Pe,e):delete t[Pe]}function ne(t){return t[Pe]}function bt(){return it()}function Wt(){return it().substring(16)}class et{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:bt(),sampleRand:Math.random()}}clone(){const e=new et;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,ee(e,ne(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&Ge(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,r=n instanceof et?n.getScopeData():tr(n)?e:void 0,{tags:s,extra:a,user:o,contexts:c,level:i,fingerprint:u=[],propagationContext:l}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...a},this._contexts={...this._contexts,...c},o&&Object.keys(o).length&&(this._user=o),i&&(this._level=i),u.length&&(this._fingerprint=u),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,ee(this,void 0),this._attachments=[],this.setPropagationContext({traceId:bt(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){var a;const r=typeof n=="number"?n:100;if(r<=0)return this;const s={timestamp:or(),...e,message:e.message?je(e.message,2048):e.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(a=this._client)==null||a.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:ne(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=ir(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=(n==null?void 0:n.event_id)||it();if(!this._client)return x.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=(r==null?void 0:r.event_id)||it();if(!this._client)return x.warn("No client configured on scope - will not capture message!"),s;const a=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:a,...r,event_id:s},this),s}captureEvent(e,n){const r=(n==null?void 0:n.event_id)||it();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(x.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class Ps{constructor(e,n){let r,s;r=e||new et,s=n||new et,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return er(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function St(){const t=he(Et());return t.stack=t.stack||new Ps(Zt("defaultCurrentScope",()=>new et),Zt("defaultIsolationScope",()=>new et))}function Ds(t){return St().withScope(t)}function Ls(t,e){const n=St();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function wn(t){return St().withScope(()=>t(St().getIsolationScope()))}function zt(t){const e=he(t);return e.acs?e.acs:{withIsolationScope:wn,withScope:Ds,withSetScope:Ls,withSetIsolationScope:(n,r)=>wn(r),getCurrentScope:()=>St().getScope(),getIsolationScope:()=>St().getIsolationScope()}}function Bt(){return zt(Et()).getCurrentScope()}function Ms(){return zt(Et()).getIsolationScope()}function vo(){return Zt("globalScope",()=>new et)}function He(...t){const e=zt(Et());if(t.length===2){const[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function ft(){return Bt().getClient()}function mo(t){const e=t.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=e,a={trace_id:n,span_id:s||Wt()};return r&&(a.parent_span_id=r),a}const re="sentry.source",cr="sentry.sample_rate",Rs="sentry.previous_trace_sample_rate",De="sentry.op",Le="sentry.origin",_o="sentry.idle_span_finish_reason",ur="sentry.measurement_unit",lr="sentry.measurement_value",xn="sentry.custom_span_name",Us="sentry.profile_id",Fs="sentry.exclusive_time",go="sentry.link.type",Vs=0,fr=1,T=2;function yo(t,e){t.setAttribute("http.response.status_code",e);const n=function(r){if(r<400&&r>=100)return{code:fr};if(r>=400&&r<500)switch(r){case 401:return{code:T,message:"unauthenticated"};case 403:return{code:T,message:"permission_denied"};case 404:return{code:T,message:"not_found"};case 409:return{code:T,message:"already_exists"};case 413:return{code:T,message:"failed_precondition"};case 429:return{code:T,message:"resource_exhausted"};case 499:return{code:T,message:"cancelled"};default:return{code:T,message:"invalid_argument"}}if(r>=500&&r<600)switch(r){case 501:return{code:T,message:"unimplemented"};case 503:return{code:T,message:"unavailable"};case 504:return{code:T,message:"deadline_exceeded"};default:return{code:T,message:"internal_error"}}return{code:T,message:"unknown_error"}}(e);n.message!=="unknown_error"&&t.setStatus(n)}const pr="_sentryScope",dr="_sentryIsolationScope";function se(t){return{scope:t[pr],isolationScope:t[dr]}}function ae(t){if(typeof t=="boolean")return Number(t);const e=typeof t=="string"?parseFloat(t):t;return typeof e!="number"||isNaN(e)||e<0||e>1?void 0:e}const hr="sentry-",Js=/^sentry-/,qs=8192;function vr(t){const e=function(r){if(!(!r||!te(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((s,a)=>{const o=En(a);return Object.entries(o).forEach(([c,i])=>{s[c]=i}),s},{}):En(r)}(t);if(!e)return;const n=Object.entries(e).reduce((r,[s,a])=>(s.match(Js)&&(r[s.slice(hr.length)]=a),r),{});return Object.keys(n).length>0?n:void 0}function bo(t){if(t)return function(e){if(Object.keys(e).length!==0)return Object.entries(e).reduce((n,[r,s],a)=>{const o=`${encodeURIComponent(r)}=${encodeURIComponent(s)}`,c=a===0?o:`${n},${o}`;return c.length>qs?(A&&x.warn(`Not adding key: ${r} with val: ${s} to baggage header due to exceeding baggage size limits.`),n):c},"")}(Object.entries(t).reduce((e,[n,r])=>(r&&(e[`${hr}${n}`]=r),e),{}))}function En(t){return t.split(",").map(e=>e.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}const Ws=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function So(t,e){const n=function(i){if(!i)return;const u=i.match(Ws);if(!u)return;let l;return u[3]==="1"?l=!0:u[3]==="0"&&(l=!1),{traceId:u[1],parentSampled:l,parentSpanId:u[2]}}(t),r=vr(e);if(!(n!=null&&n.traceId))return{traceId:bt(),sampleRand:Math.random()};const s=function(i,u){const l=ae(u==null?void 0:u.sample_rand);if(l!==void 0)return l;const f=ae(u==null?void 0:u.sample_rate);return f&&(i==null?void 0:i.parentSampled)!==void 0?i.parentSampled?Math.random()*f:f+Math.random()*(1-f):Math.random()}(n,r);r&&(r.sample_rand=s.toString());const{traceId:a,parentSpanId:o,parentSampled:c}=n;return{traceId:a,parentSpanId:o,sampled:c,dsc:r||{},sampleRand:s}}function zs(t=bt(),e=Wt(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}const Qe=1;let kn=!1;function Bs(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:s,parent_span_id:a,status:o,origin:c,links:i}=V(t);return{parent_span_id:a,span_id:e,trace_id:n,data:r,op:s,status:o,origin:c,links:i}}function wo(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext(),s=r?e:V(t).parent_span_id,a=se(t).scope;return{parent_span_id:s,span_id:r?(a==null?void 0:a.getPropagationContext().propagationSpanId)||Wt():e,trace_id:n}}function xo(t){const{traceId:e,spanId:n}=t.spanContext();return zs(e,n,gt(t))}function mr(t){return t&&t.length>0?t.map(({context:{spanId:e,traceId:n,traceFlags:r,...s},attributes:a})=>({span_id:e,trace_id:n,sampled:r===Qe,attributes:a,...s})):void 0}function _t(t){return typeof t=="number"?In(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?In(t.getTime()):Dt()}function In(t){return t>9999999999?t/1e3:t}function V(t){var r;if(function(s){return typeof s.getSpanJSON=="function"}(t))return t.getSpanJSON();const{spanId:e,traceId:n}=t.spanContext();if(function(s){const a=s;return!!(a.attributes&&a.startTime&&a.name&&a.endTime&&a.status)}(t)){const{attributes:s,startTime:a,name:o,endTime:c,status:i,links:u}=t;return{span_id:e,trace_id:n,data:s,description:o,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?(r=t.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:_t(a),timestamp:_t(c)||void 0,status:_r(i),op:s[De],origin:s[Le],links:mr(u)}}return{span_id:e,trace_id:n,start_timestamp:0,data:{}}}function gt(t){const{traceFlags:e}=t.spanContext();return e===Qe}function _r(t){if(t&&t.code!==Vs)return t.code===fr?"ok":t.message||"unknown_error"}const ct="_sentryChildSpans",Me="_sentryRootSpan";function An(t,e){const n=t[Me]||t;tt(e,Me,n),t[ct]?t[ct].add(e):tt(t,ct,new Set([e]))}function Eo(t,e){t[ct]&&t[ct].delete(e)}function Ks(t){const e=new Set;return function n(r){if(!e.has(r)&&gt(r)){e.add(r);const s=r[ct]?Array.from(r[ct]):[];for(const a of s)n(a)}}(t),Array.from(e)}function X(t){return t[Me]||t}function Ys(){const t=zt(Et());return t.getActiveSpan?t.getActiveSpan():ne(Bt())}function Gs(){kn||(Ye(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),kn=!0)}const On=50,Hs="?",$n=/\(error: (.*)\)/,Tn=/captureMessage|captureException/;function Qs(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const a=[],o=n.split(`
`);for(let c=r;c<o.length;c++){const i=o[c];if(i.length>1024)continue;const u=$n.test(i)?i.replace($n,"$1"):i;if(!u.match(/\S*Error: /)){for(const l of e){const f=l(u);if(f){a.push(f);break}}if(a.length>=On+s)break}}return function(c){if(!c.length)return[];const i=Array.from(c);return/sentryWrapped/.test(Xt(i).function||"")&&i.pop(),i.reverse(),Tn.test(Xt(i).function||"")&&(i.pop(),Tn.test(Xt(i).function||"")&&i.pop()),i.slice(0,On).map(u=>({...u,filename:u.filename||Xt(i).filename,function:u.function||Hs}))}(a.slice(s))}}function ko(t){return Array.isArray(t)?Qs(...t):t}function Xt(t){return t[t.length-1]||{}}const Nn="<anonymous>";function Xs(t){try{return t&&typeof t=="function"&&t.name||Nn}catch{return Nn}}function Io(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}function Xe(t){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const e=t||((n=ft())==null?void 0:n.getOptions());return!(!e||e.tracesSampleRate==null&&!e.tracesSampler)}const Zs="production",ta=/^o(\d+)\./,ea=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Ze(t,e=!1){const{host:n,path:r,pass:s,port:a,projectId:o,protocol:c,publicKey:i}=t;return`${c}://${i}${e&&s?`:${s}`:""}@${n}${a?`:${a}`:""}/${r&&`${r}/`}${o}`}function Cn(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function Ao(t){const e=typeof t=="string"?function(n){const r=ea.exec(n);if(!r)return void Ye(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[s,a,o="",c="",i="",u=""]=r.slice(1);let l="",f=u;const p=f.split("/");if(p.length>1&&(l=p.slice(0,-1).join("/"),f=p.pop()),f){const h=f.match(/^\d+/);h&&(f=h[0])}return Cn({host:c,pass:o,path:l,projectId:f,port:i,protocol:s,publicKey:a})}(t):Cn(t);if(e&&function(n){if(!A)return!0;const{port:r,projectId:s,protocol:a}=n;return!(["protocol","publicKey","host","projectId"].find(o=>!n[o]&&(x.error(`Invalid Sentry Dsn: ${o} missing`),!0))||(s.match(/^\d+$/)?function(o){return o==="http"||o==="https"}(a)?r&&isNaN(parseInt(r,10))&&(x.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(x.error(`Invalid Sentry Dsn: Invalid protocol ${a}`),1):(x.error(`Invalid Sentry Dsn: Invalid projectId ${s}`),1)))}(e))return e}const gr="_frozenDsc";function Ae(t,e){tt(t,gr,e)}function yr(t,e){const n=e.getOptions(),{publicKey:r,host:s}=e.getDsn()||{};let a;n.orgId?a=String(n.orgId):s&&(a=function(c){const i=c.match(ta);return i==null?void 0:i[1]}(s));const o={environment:n.environment||Zs,release:n.release,public_key:r,trace_id:t,org_id:a};return e.emit("createDsc",o),o}function Oo(t,e){const n=e.getPropagationContext();return n.dsc||yr(n.traceId,t)}function oe(t){var v;const e=ft();if(!e)return{};const n=X(t),r=V(n),s=r.data,a=n.spanContext().traceState,o=(a==null?void 0:a.get("sentry.sample_rate"))??s[cr]??s[Rs];function c(d){return typeof o!="number"&&typeof o!="string"||(d.sample_rate=`${o}`),d}const i=n[gr];if(i)return c(i);const u=a==null?void 0:a.get("sentry.dsc"),l=u&&vr(u);if(l)return c(l);const f=yr(t.spanContext().traceId,e),p=s[re],h=r.description;return p!=="url"&&h&&(f.transaction=h),Xe()&&(f.sampled=String(gt(n)),f.sample_rand=(a==null?void 0:a.get("sentry.sample_rand"))??((v=se(n).scope)==null?void 0:v.getPropagationContext().sampleRand.toString())),c(f),e.emit("createDsc",f,n),f}class ie{constructor(e={}){this._traceId=e.traceId||bt(),this._spanId=e.spanId||Wt()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,n){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,n,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,n){}}function na(t,e,n=()=>{}){let r;try{r=t()}catch(s){throw e(s),n(),s}return function(s,a,o){return er(s)?s.then(c=>(o(),c),c=>{throw a(c),o(),c}):(o(),s)}(r,e,n)}function br(t,e=100,n=1/0){try{return Re("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function ra(t,e=3,n=102400){const r=br(t,e);return s=r,function(a){return~-encodeURI(a).split(/%..|./).length}(JSON.stringify(s))>n?ra(t,e-1,n):r;var s}function Re(t,e,n=1/0,r=1/0,s=function(){const a=new WeakSet;function o(i){return!!a.has(i)||(a.add(i),!1)}function c(i){a.delete(i)}return[o,c]}()){const[a,o]=s;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const c=function(h,v){try{if(h==="domain"&&v&&typeof v=="object"&&v._events)return"[Domain]";if(h==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&v===global)return"[Global]";if(typeof window<"u"&&v===window)return"[Window]";if(typeof document<"u"&&v===document)return"[Document]";if(nr(v))return"[VueViewModel]";if(tr(d=v)&&"nativeEvent"in d&&"preventDefault"in d&&"stopPropagation"in d)return"[SyntheticEvent]";if(typeof v=="number"&&!Number.isFinite(v))return`[${v}]`;if(typeof v=="function")return`[Function: ${Xs(v)}]`;if(typeof v=="symbol")return`[${String(v)}]`;if(typeof v=="bigint")return`[BigInt: ${String(v)}]`;const m=function(_){const E=Object.getPrototypeOf(_);return E!=null&&E.constructor?E.constructor.name:"null prototype"}(v);return/^HTML(\w*)Element$/.test(m)?`[HTMLElement: ${m}]`:`[object ${m}]`}catch(m){return`**non-serializable** (${m})`}var d}(t,e);if(!c.startsWith("[object "))return c;if(e.__sentry_skip_normalization__)return e;const i=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(i===0)return c.replace("object ","");if(a(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{return Re("",u.toJSON(),i-1,r,s)}catch{}const l=Array.isArray(e)?[]:{};let f=0;const p=rr(e);for(const h in p){if(!Object.prototype.hasOwnProperty.call(p,h))continue;if(f>=r){l[h]="[MaxProperties ~]";break}const v=p[h];l[h]=Re(h,v,i-1,r,s),f++}return o(e),l}function tn(t,e=[]){return[t,e]}function $o(t,e){const[n,r]=t;return[n,[...r,e]]}function To(t,e){const n=t[1];for(const r of n)if(e(r,r[0].type))return!0;return!1}function Ue(t){const e=he(q);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}function No(t){const[e,n]=t;let r=JSON.stringify(e);function s(a){typeof r=="string"?r=typeof a=="string"?r+a:[Ue(r),a]:r.push(typeof a=="string"?Ue(a):a)}for(const a of n){const[o,c]=a;if(s(`
${JSON.stringify(o)}
`),typeof c=="string"||c instanceof Uint8Array)s(c);else{let i;try{i=JSON.stringify(c)}catch{i=JSON.stringify(br(c))}s(i)}}return typeof r=="string"?r:function(a){const o=a.reduce((u,l)=>u+l.length,0),c=new Uint8Array(o);let i=0;for(const u of a)c.set(u,i),i+=u.length;return c}(r)}function sa(t){return[{type:"span"},t]}function Co(t){const e=typeof t.data=="string"?Ue(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}const aa={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function jo(t){return aa[t]}function Sr(t){if(!(t!=null&&t.sdk))return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function Po(t,e,n,r){const s=Sr(n);return tn({sent_at:new Date().toISOString(),...s&&{sdk:s},...!!r&&e&&{dsn:Ze(e)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}function Do(t,e,n,r){const s=Sr(n),a=t.type&&t.type!=="replay_event"?t.type:"event";(function(c,i){i&&(c.sdk=c.sdk||{},c.sdk.name=c.sdk.name||i.name,c.sdk.version=c.sdk.version||i.version,c.sdk.integrations=[...c.sdk.integrations||[],...i.integrations||[]],c.sdk.packages=[...c.sdk.packages||[],...i.packages||[]])})(t,n==null?void 0:n.sdk);const o=function(c,i,u,l){var p;const f=(p=c.sdkProcessingMetadata)==null?void 0:p.dynamicSamplingContext;return{event_id:c.event_id,sent_at:new Date().toISOString(),...i&&{sdk:i},...!!u&&l&&{dsn:Ze(l)},...f&&{trace:f}}}(t,s,r,e);return delete t.sdkProcessingMetadata,tn(o,[[{type:a},t]])}function Lo(t,e,n,r=Ys()){const s=r&&X(r);s&&(A&&x.log(`[Measurement] Setting measurement on root span: ${t} = ${e} ${n}`),s.addEvent(t,{[lr]:e,[ur]:n}))}function jn(t){if(!t||t.length===0)return;const e={};return t.forEach(n=>{const r=n.attributes||{},s=r[ur],a=r[lr];typeof s=="string"&&typeof a=="number"&&(e[n.name]={value:a,unit:s})}),e}class ve{constructor(e={}){this._traceId=e.traceId||bt(),this._spanId=e.spanId||Wt(),this._startTime=e.startTimestamp||Dt(),this._links=e.links,this._attributes={},this.setAttributes({[Le]:"manual",[De]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,n){}spanContext(){const{_spanId:e,_traceId:n,_sampled:r}=this;return{spanId:e,traceId:n,traceFlags:r?Qe:0}}setAttribute(e,n){return n===void 0?delete this._attributes[e]:this._attributes[e]=n,this}setAttributes(e){return Object.keys(e).forEach(n=>this.setAttribute(n,e[n])),this}updateStartTime(e){this._startTime=_t(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(re,"custom"),this}end(e){this._endTime||(this._endTime=_t(e),function(n){if(!A)return;const{description:r="< unknown name >",op:s="< unknown op >"}=V(n),{spanId:a}=n.spanContext(),o=`[Tracing] Finishing "${s}" ${X(n)===n?"root ":""}span "${r}" with ID ${a}`;x.log(o)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[De],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:_r(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[Le],profile_id:this._attributes[Us],exclusive_time:this._attributes[Fs],measurements:jn(this._events),is_segment:this._isStandaloneSpan&&X(this)===this||void 0,segment_id:this._isStandaloneSpan?X(this).spanContext().spanId:void 0,links:mr(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,n,r){A&&x.log("[Tracing] Adding an event to span:",e);const s=Pn(n)?n:r||Dt(),a=Pn(n)?{}:n||{},o={name:e,time:_t(s),attributes:a};return this._events.push(o),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const e=ft();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===X(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(r){const s=ft();if(!s)return;const a=r[1];if(!a||a.length===0)return void s.recordDroppedEvent("before_send","span");s.sendEnvelope(r)}(function(r,s){const a=oe(r[0]),o=s==null?void 0:s.getDsn(),c=s==null?void 0:s.getOptions().tunnel,i={sent_at:new Date().toISOString(),...function(p){return!!p.trace_id&&!!p.public_key}(a)&&{trace:a},...!!c&&o&&{dsn:Ze(o)}},u=s==null?void 0:s.getOptions().beforeSendSpan,l=u?p=>{const h=V(p);return u(h)||(Gs(),h)}:V,f=[];for(const p of r){const h=l(p);h&&f.push(sa(h))}return tn(i,f)}([this],e)):(A&&x.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();n&&(se(this).scope||Bt()).captureEvent(n)}_convertSpanToTransaction(){var i;if(!Dn(V(this)))return;this._name||(A&&x.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:e,isolationScope:n}=se(this),r=(i=e==null?void 0:e.getScopeData().sdkProcessingMetadata)==null?void 0:i.normalizedRequest;if(this._sampled!==!0)return;const s=Ks(this).filter(u=>u!==this&&!function(l){return l instanceof ve&&l.isStandaloneSpan()}(u)).map(u=>V(u)).filter(Dn),a=this._attributes[re];delete this._attributes[xn],s.forEach(u=>{delete u.data[xn]});const o={contexts:{trace:Bs(this)},spans:s.length>1e3?s.sort((u,l)=>u.start_timestamp-l.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:oe(this)},request:r,...a&&{transaction_info:{source:a}}},c=jn(this._events);return c&&Object.keys(c).length&&(A&&x.log("[Measurements] Adding measurements to transaction event",JSON.stringify(c,void 0,2)),o.measurements=c),o}}function Pn(t){return t&&typeof t=="number"||t instanceof Date||Array.isArray(t)}function Dn(t){return!!(t.start_timestamp&&t.timestamp&&t.span_id&&t.trace_id)}const wr="__SENTRY_SUPPRESS_TRACING__";function Mo(t,e){const n=en();if(n.startSpan)return n.startSpan(t,e);const r=kr(t),{forceTransaction:s,parentSpan:a,scope:o}=t,c=o==null?void 0:o.clone();return He(c,()=>{const i=(u=a)!==void 0?l=>xr(u,l):l=>l();var u;return i(()=>{const l=Bt(),f=Ir(l),p=t.onlyIfParent&&!f?new ie:Er({parentSpan:f,spanArguments:r,forceTransaction:s,scope:l});return ee(l,p),na(()=>e(p),()=>{const{status:h}=V(p);!p.isRecording()||h&&h!=="ok"||p.setStatus({code:T,message:"internal_error"})},()=>{p.end()})})})}function Ro(t){const e=en();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=kr(t),{forceTransaction:r,parentSpan:s}=t;return(t.scope?a=>He(t.scope,a):s!==void 0?a=>xr(s,a):a=>a())(()=>{const a=Bt(),o=Ir(a);return t.onlyIfParent&&!o?new ie:Er({parentSpan:o,spanArguments:n,forceTransaction:r,scope:a})})}function xr(t,e){const n=en();return n.withActiveSpan?n.withActiveSpan(t,e):He(r=>(ee(r,t||void 0),e(r)))}function Er({parentSpan:t,spanArguments:e,forceTransaction:n,scope:r}){if(!Xe()){const o=new ie;return(n||!t)&&Ae(o,{sampled:"false",sample_rate:"0",transaction:e.name,...oe(o)}),o}const s=Ms();let a;if(t&&!n)a=function(o,c,i){const{spanId:u,traceId:l}=o.spanContext(),f=!c.getScopeData().sdkProcessingMetadata[wr]&&gt(o),p=f?new ve({...i,parentSpanId:u,traceId:l,sampled:f}):new ie({traceId:l});An(o,p);const h=ft();return h&&(h.emit("spanStart",p),i.endTimestamp&&h.emit("spanEnd",p)),p}(t,r,e),An(t,a);else if(t){const o=oe(t),{traceId:c,spanId:i}=t.spanContext(),u=gt(t);a=Ln({traceId:c,parentSpanId:i,...e},r,u),Ae(a,o)}else{const{traceId:o,dsc:c,parentSpanId:i,sampled:u}={...s.getPropagationContext(),...r.getPropagationContext()};a=Ln({traceId:o,parentSpanId:i,...e},r,u),c&&Ae(a,c)}return function(o){if(!A)return;const{description:c="< unknown name >",op:i="< unknown op >",parent_span_id:u}=V(o),{spanId:l}=o.spanContext(),f=gt(o),p=X(o),h=p===o,v=`[Tracing] Starting ${f?"sampled":"unsampled"} ${h?"root ":""}span`,d=[`op: ${i}`,`name: ${c}`,`ID: ${l}`];if(u&&d.push(`parent ID: ${u}`),!h){const{op:m,description:_}=V(p);d.push(`root ID: ${p.spanContext().spanId}`),m&&d.push(`root op: ${m}`),_&&d.push(`root description: ${_}`)}x.log(`${v}
  ${d.join(`
  `)}`)}(a),function(o,c,i){o&&(tt(o,dr,i),tt(o,pr,c))}(a,r,s),a}function kr(t){const e={isStandalone:(t.experimental||{}).standalone,...t};if(t.startTime){const n={...e};return n.startTimestamp=_t(t.startTime),delete n.startTime,n}return e}function en(){return zt(Et())}function Ln(t,e,n){var v;const r=ft(),s=(r==null?void 0:r.getOptions())||{},{name:a=""}=t,o={spanAttributes:{...t.attributes},spanName:a,parentSampled:n};r==null||r.emit("beforeSampling",o,{decision:!1});const c=o.parentSampled??n,i=o.spanAttributes,u=e.getPropagationContext(),[l,f,p]=e.getScopeData().sdkProcessingMetadata[wr]?[!1]:function(d,m,_){if(!Xe(d))return[!1];let E,I;typeof d.tracesSampler=="function"?(I=d.tracesSampler({...m,inheritOrSampleWith:xe=>typeof m.parentSampleRate=="number"?m.parentSampleRate:typeof m.parentSampled=="boolean"?Number(m.parentSampled):xe}),E=!0):m.parentSampled!==void 0?I=m.parentSampled:d.tracesSampleRate!==void 0&&(I=d.tracesSampleRate,E=!0);const Y=ae(I);if(Y===void 0)return A&&x.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(I)} of type ${JSON.stringify(typeof I)}.`),[!1];if(!Y)return A&&x.log("[Tracing] Discarding transaction because "+(typeof d.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,Y,E];const dt=_<Y;return dt||A&&x.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(I)})`),[dt,Y,E]}(s,{name:a,parentSampled:c,attributes:i,parentSampleRate:ae((v=u.dsc)==null?void 0:v.sample_rate)},u.sampleRand),h=new ve({...t,attributes:{[re]:"custom",[cr]:f!==void 0&&p?f:void 0,...i},sampled:l});return!l&&r&&(A&&x.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",h),h}function Ir(t){const e=ne(t);if(!e)return;const n=ft();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?X(e):e}const oa=!1;var me=Array.isArray,ia=Array.prototype.indexOf,ca=Array.from,ua=Object.defineProperty,jt=Object.getOwnPropertyDescriptor,Ar=Object.getOwnPropertyDescriptors,la=Object.prototype,fa=Array.prototype,nn=Object.getPrototypeOf,Mn=Object.isExtensible;function Uo(t){return typeof t=="function"}const yt=()=>{};function Fo(t){return typeof(t==null?void 0:t.then)=="function"}function pa(t){return t()}function Lt(t){for(var e=0;e<t.length;e++)t[e]()}function Vo(t,e,n=!1){return t===void 0?n?e():e:t}function Jo(t,e){if(Array.isArray(t))return t;if(!(Symbol.iterator in t))return Array.from(t);const n=[];for(const r of t)if(n.push(r),n.length===e)break;return n}const U=2,rn=4,Kt=8,sn=16,H=32,It=64,an=128,P=256,ce=512,D=1024,G=2048,st=4096,wt=8192,on=16384,Or=32768,cn=65536,Rn=1<<17,da=1<<18,$r=1<<19,Fe=1<<20,un=1<<21,ut=Symbol("$state"),qo=Symbol("legacy props"),ha=Symbol(""),Tr=new class extends Error{constructor(){super(...arguments);ke(this,"name","StaleReactionError");ke(this,"message","The reaction that called `getAbortSignal()` was re-run or destroyed")}};function Nr(t){return t===this.v}function Cr(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function Wo(t,e){return t!==e}function jr(t){return!Cr(t,this.v)}function zo(t){throw new Error("https://svelte.dev/e/props_invalid_value")}let Yt=!1,va=!1;const Bo=1,Ko=2,Yo=4,Go=8,Ho=16,Qo=1,Xo=2,Zo=4,ti=8,ei=16,ni=1,ri=2,si=4,N=Symbol(),ma="http://www.w3.org/1999/xhtml",ai="http://www.w3.org/2000/svg";function oi(){throw new Error("https://svelte.dev/e/invalid_default_snippet")}function Gt(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let g=null;function Un(t){g=t}function ii(t){return Pr().get(t)}function ci(t,e){return Pr().set(t,e),e}function _a(t,e=!1,n){var r=g={p:g,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};Yt&&!e&&(g.l={s:null,u:null,r1:[],r2:ln(!1)}),ge(()=>{r.d=!0})}function ga(t){const e=g;if(e!==null){t!==void 0&&(e.x=t);const o=e.e;if(o!==null){var n=b,r=y;e.e=null;try{for(var s=0;s<o.length;s++){var a=o[s];nt(a.effect),K(a.reaction),Wr(a.fn)}}finally{nt(n),K(r)}}g=e.p,e.m=!0}return t||{}}function _e(){return!Yt||g!==null&&g.l===null}function Pr(t){return g===null&&Gt(),g.c??(g.c=new Map(function(e){let n=e.p;for(;n!==null;){const r=n.c;if(r!==null)return r;n=n.p}return null}(g)||void 0))}function Tt(t){if(typeof t!="object"||t===null||ut in t)return t;const e=nn(t);if(e!==la&&e!==fa)return t;var n=new Map,r=me(t),s=Q(0),a=y,o=c=>{var i=y;K(a);var u=c();return K(i),u};return r&&n.set("length",Q(t.length)),new Proxy(t,{defineProperty(c,i,u){"value"in u&&u.configurable!==!1&&u.enumerable!==!1&&u.writable!==!1||function(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}();var l=n.get(i);return l===void 0?l=o(()=>{var f=Q(u.value);return n.set(i,f),f}):F(l,u.value,!0),!0},deleteProperty(c,i){var u=n.get(i);if(u===void 0){if(i in c){const p=o(()=>Q(N));n.set(i,p),Oe(s)}}else{if(r&&typeof i=="string"){var l=n.get("length"),f=Number(i);Number.isInteger(f)&&f<l.v&&F(l,f)}F(u,N),Oe(s)}return!0},get(c,i,u){var h;if(i===ut)return t;var l=n.get(i),f=i in c;if(l!==void 0||f&&!((h=jt(c,i))!=null&&h.writable)||(l=o(()=>Q(Tt(f?c[i]:N))),n.set(i,l)),l!==void 0){var p=J(l);return p===N?void 0:p}return Reflect.get(c,i,u)},getOwnPropertyDescriptor(c,i){var u=Reflect.getOwnPropertyDescriptor(c,i);if(u&&"value"in u){var l=n.get(i);l&&(u.value=J(l))}else if(u===void 0){var f=n.get(i),p=f==null?void 0:f.v;if(f!==void 0&&p!==N)return{enumerable:!0,configurable:!0,value:p,writable:!0}}return u},has(c,i){var f;if(i===ut)return!0;var u=n.get(i),l=u!==void 0&&u.v!==N||Reflect.has(c,i);return(u!==void 0||b!==null&&(!l||(f=jt(c,i))!=null&&f.writable))&&(u===void 0&&(u=o(()=>Q(l?Tt(c[i]):N)),n.set(i,u)),J(u)===N)?!1:l},set(c,i,u,l){var E;var f=n.get(i),p=i in c;if(r&&i==="length")for(var h=u;h<f.v;h+=1){var v=n.get(h+"");v!==void 0?F(v,N):h in c&&(v=o(()=>Q(N)),n.set(h+"",v))}f===void 0?p&&!((E=jt(c,i))!=null&&E.writable)||(F(f=o(()=>Q(void 0)),Tt(u)),n.set(i,f)):(p=f.v!==N,F(f,o(()=>Tt(u))));var d=Reflect.getOwnPropertyDescriptor(c,i);if(d!=null&&d.set&&d.set.call(l,u),!p){if(r&&typeof i=="string"){var m=n.get("length"),_=Number(i);Number.isInteger(_)&&_>=m.v&&F(m,_+1)}Oe(s)}return!0},ownKeys(c){J(s);var i=Reflect.ownKeys(c).filter(f=>{var p=n.get(f);return p===void 0||p.v!==N});for(var[u,l]of n)l.v===N||u in c||i.push(u);return i},setPrototypeOf(){(function(){throw new Error("https://svelte.dev/e/state_prototype_fixed")})()}})}function Oe(t,e=1){F(t,t.v+e)}function Fn(t){try{if(t!==null&&typeof t=="object"&&ut in t)return t[ut]}catch{}return t}function Ht(t){var e=U|G,n=y!==null&&y.f&U?y:null;return b===null||n!==null&&n.f&P?e|=P:b.f|=$r,{ctx:g,deps:null,effects:null,equals:Nr,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??b,ac:null}}function ui(t){const e=Ht(t);return es(e),e}function li(t){const e=Ht(t);return e.equals=jr,e}function Dr(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)B(e[n])}}function Lr(t){var e,n=b;nt(function(r){for(var s=r.parent;s!==null;){if(!(s.f&U))return s;s=s.parent}return null}(t));try{Dr(t),e=ss(t)}finally{nt(n)}return e}function Mr(t){var e=Lr(t);t.equals(e)||(t.v=e,t.wv=ns()),At||W(t,(Z||t.f&P)&&t.deps!==null?st:D)}const Mt=new Map;function ln(t,e){return{f:0,v:t,reactions:null,equals:Nr,rv:0,wv:0}}function Q(t,e){const n=ln(t);return es(n),n}function fi(t,e=!1,n=!0){var s;const r=ln(t);return e||(r.equals=jr),Yt&&n&&g!==null&&g.l!==null&&((s=g.l).s??(s.s=[])).push(r),r}function pi(t,e){return F(t,rt(()=>J(t))),e}function F(t,e,n=!1){return y!==null&&(!z||y.f&Rn)&&_e()&&y.f&(U|sn|Rn)&&((k==null?void 0:k.reaction)!==y||!k.sources.includes(t))&&function(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}(),Rr(t,n?Tt(e):e)}function Rr(t,e){if(!t.equals(e)){var n=t.v;At?Mt.set(t,e):Mt.set(t,n),t.v=e,t.f&U&&(t.f&G&&Lr(t),W(t,t.f&P?st:D)),t.wv=ns(),Ur(t,G),_e()&&b!==null&&b.f&D&&!(b.f&(H|It))&&(R===null?function(r){R=r}([t]):R.push(t))}return e}function di(t,e=1){var n=J(t),r=e===1?n++:n--;return F(t,n),r}function Ur(t,e){var n=t.reactions;if(n!==null)for(var r=_e(),s=n.length,a=0;a<s;a++){var o=n[a],c=o.f;c&G||(r||o!==b)&&(W(o,e),c&(D|P)&&(c&U?Ur(o,st):Se(o)))}}var Vn,ya,Fr,Vr,Jr;function fn(t=""){return document.createTextNode(t)}function xt(t){return Vr.call(t)}function pn(t){return Jr.call(t)}function hi(t,e){return xt(t)}function vi(t,e){var n=xt(t);return n instanceof Comment&&n.data===""?pn(n):n}function mi(t,e=1,n=!1){let r=t;for(;e--;)r=pn(r);return r}function _i(t){t.textContent=""}function qr(t){b===null&&y===null&&function(e){throw new Error("https://svelte.dev/e/effect_orphan")}(),y!==null&&y.f&P&&b===null&&function(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}(),At&&function(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}()}function at(t,e,n,r=!0){var s=b,a={ctx:g,deps:null,nodes_start:null,nodes_end:null,f:t|G,first:null,fn:e,last:null,next:null,parent:s,b:s&&s.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{be(a),a.f|=Or}catch(c){throw B(a),c}else e!==null&&Se(a);if(!(n&&a.deps===null&&a.first===null&&a.nodes_start===null&&a.teardown===null&&!(a.f&($r|an)))&&r&&(s!==null&&function(c,i){var u=i.last;u===null?i.last=i.first=c:(u.next=c,c.prev=u,i.last=c)}(a,s),y!==null&&y.f&U)){var o=y;(o.effects??(o.effects=[])).push(a)}return a}function ge(t){const e=at(Kt,null,!1);return W(e,D),e.teardown=t,e}function Ve(t){if(qr(),!(b!==null&&b.f&H&&g!==null&&!g.m))return Wr(t);var e=g;(e.e??(e.e=[])).push({fn:t,effect:b,reaction:y})}function Wr(t){return at(rn|un,t,!1)}function zr(t){return at(rn,t,!1)}function gi(t,e){var n=g,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=Br(()=>{t(),r.ran||(r.ran=!0,F(n.l.r2,!0),rt(e))})}function yi(){var t=g;Br(()=>{if(J(t.l.r2)){for(var e of t.l.r1){var n=e.effect;n.f&D&&W(n,st),Qt(n)&&be(n),e.ran=!1}t.l.r2.v=!1}})}function Br(t){return at(Kt,t,!0)}function bi(t,e=[],n=Ht){const r=e.map(n);return ye(()=>t(...r.map(J)))}function ye(t,e=0){return at(Kt|sn|e,t,!0)}function Rt(t,e=!0){return at(Kt|H,t,!0,e)}function Kr(t){var e=t.teardown;if(e!==null){const n=At,r=y;qn(!0),K(null);try{e.call(null)}finally{qn(n),K(r)}}}function Yr(t,e=!1){var s;var n=t.first;for(t.first=t.last=null;n!==null;){(s=n.ac)==null||s.abort(Tr);var r=n.next;n.f&It?n.parent=null:B(n,e),n=r}}function B(t,e=!0){var n=!1;(e||t.f&da)&&t.nodes_start!==null&&t.nodes_end!==null&&(ba(t.nodes_start,t.nodes_end),n=!0),Yr(t,e&&!n),fe(t,0),W(t,on);var r=t.transitions;if(r!==null)for(const a of r)a.stop();Kr(t);var s=t.parent;s!==null&&s.first!==null&&Gr(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=t.ac=null}function ba(t,e){for(;t!==null;){var n=t===e?null:pn(t);t.remove(),t=n}}function Gr(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function Je(t,e){var n=[];Hr(t,n,!0),Sa(n,()=>{B(t),e&&e()})}function Sa(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var s of t)s.out(r)}else e()}function Hr(t,e,n){if(!(t.f&wt)){if(t.f^=wt,t.transitions!==null)for(const a of t.transitions)(a.is_global||n)&&e.push(a);for(var r=t.first;r!==null;){var s=r.next;Hr(r,e,!!(r.f&cn||r.f&H)&&n),r=s}}}function Jn(t){Qr(t,!0)}function Qr(t,e){if(t.f&wt){t.f^=wt;for(var n=t.first;n!==null;){var r=n.next;Qr(n,!!(n.f&cn||n.f&H)&&e),n=r}if(t.transitions!==null)for(const s of t.transitions)(s.is_global||e)&&s.in()}}let Ut=[],$e=[];function Xr(){var t=Ut;Ut=[],Lt(t)}function Zr(t){Ut.length===0&&queueMicrotask(Xr),Ut.push(t)}function wa(){var t;Ut.length>0&&Xr(),$e.length>0&&(t=$e,$e=[],Lt(t))}function ts(t,e){for(;e!==null;){if(e.f&an)try{return void e.b.error(t)}catch{}e=e.parent}throw t}let Ft=!1,Vt=null,lt=!1,At=!1;function qn(t){At=t}let Pt=[],y=null,z=!1;function K(t){y=t}let b=null;function nt(t){b=t}let k=null;function es(t){y!==null&&y.f&Fe&&(k===null?k={reaction:y,sources:[t]}:k.sources.push(t))}let $=null,j=0,R=null,ue=1,le=0,Z=!1,ot=null;function ns(){return++ue}function Qt(t){var f;var e=t.f;if(e&G)return!0;if(e&st){var n=t.deps,r=!!(e&P);if(n!==null){var s,a,o=!!(e&ce),c=r&&b!==null&&!Z,i=n.length;if(o||c){var u=t,l=u.parent;for(s=0;s<i;s++)a=n[s],!o&&((f=a==null?void 0:a.reactions)!=null&&f.includes(u))||(a.reactions??(a.reactions=[])).push(u);o&&(u.f^=ce),!c||l===null||l.f&P||(u.f^=P)}for(s=0;s<i;s++)if(Qt(a=n[s])&&Mr(a),a.wv>t.wv)return!0}r&&(b===null||Z)||W(t,D)}return!1}function rs(t,e,n=!0){var r=t.reactions;if(r!==null)for(var s=0;s<r.length;s++){var a=r[s];(k==null?void 0:k.reaction)===y&&k.sources.includes(t)||(a.f&U?rs(a,e,!1):e===a&&(n?W(a,G):a.f&D&&W(a,st),Se(a)))}}function ss(t){var h;var e=$,n=j,r=R,s=y,a=Z,o=k,c=g,i=z,u=t.f;$=null,j=0,R=null,Z=!!(u&P)&&(z||!lt||y===null),y=u&(H|It)?null:t,k=null,Un(t.ctx),z=!1,le++,t.f|=Fe,t.ac!==null&&(t.ac.abort(Tr),t.ac=null);try{var l=(0,t.fn)(),f=t.deps;if($!==null){var p;if(fe(t,j),f!==null&&j>0)for(f.length=j+$.length,p=0;p<$.length;p++)f[j+p]=$[p];else t.deps=f=$;if(!Z||u&U&&t.reactions!==null)for(p=j;p<f.length;p++)((h=f[p]).reactions??(h.reactions=[])).push(t)}else f!==null&&j<f.length&&(fe(t,j),f.length=j);if(_e()&&R!==null&&!z&&f!==null&&!(t.f&(U|st|G)))for(p=0;p<R.length;p++)rs(R[p],t);return s!==null&&s!==t&&(le++,R!==null&&(r===null?r=R:r.push(...R))),l}catch(v){(function(d){var m=b;if(m.f&Or)ts(d,m);else{if(!(m.f&an))throw d;m.fn(d)}})(v)}finally{$=e,j=n,R=r,y=s,Z=a,k=o,Un(c),z=i,t.f^=Fe}}function xa(t,e){let n=e.reactions;if(n!==null){var r=ia.call(n,t);if(r!==-1){var s=n.length-1;s===0?n=e.reactions=null:(n[r]=n[s],n.pop())}}n===null&&e.f&U&&($===null||!$.includes(e))&&(W(e,st),e.f&(P|ce)||(e.f^=ce),Dr(e),fe(e,0))}function fe(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)xa(t,n[r])}function be(t){var e=t.f;if(!(e&on)){W(t,D);var n=b,r=lt;b=t,lt=!0;try{e&sn?function(a){for(var o=a.first;o!==null;){var c=o.next;o.f&H||B(o),o=c}}(t):Yr(t),Kr(t);var s=ss(t);t.teardown=typeof s=="function"?s:null,t.wv=ue,oa&&va&&t.f&G&&t.deps}finally{lt=r,b=n}}}function Ea(){try{(function(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")})()}catch(t){if(Vt===null)throw t;ts(t,Vt)}}function as(){var t=lt;try{var e=0;for(lt=!0;Pt.length>0;){e++>1e3&&Ea();var n=Pt,r=n.length;Pt=[];for(var s=0;s<r;s++)ka(Ia(n[s]));Mt.clear()}}finally{Ft=!1,lt=t,Vt=null}}function ka(t){var e=t.length;if(e!==0){for(var n=0;n<e;n++){var r=t[n];if(!(r.f&(on|wt))&&Qt(r)){var s=ue;if(be(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Gr(r):r.fn=null),ue>s&&r.f&un)break}}for(;n<e;n+=1)Se(t[n])}}function Se(t){Ft||(Ft=!0,queueMicrotask(as));for(var e=Vt=t;e.parent!==null;){var n=(e=e.parent).f;if(n&(It|H)){if(!(n&D))return;e.f^=D}}Pt.push(e)}function Ia(t){for(var e=[],n=t;n!==null;){var r=n.f,s=!!(r&(H|It));if(!(s&&r&D||r&wt)){r&rn?e.push(n):s?n.f^=D:Qt(n)&&be(n);var a=n.first;if(a!==null){n=a;continue}}var o=n.parent;for(n=n.next;n===null&&o!==null;)n=o.next,o=o.parent}return e}function Aa(t){for(;;){if(wa(),Pt.length===0)return Ft=!1,void(Vt=null);Ft=!0,as()}}async function Si(){await Promise.resolve(),Aa()}function J(t){var e=!!(t.f&U);if(ot!==null&&ot.add(t),y===null||z){if(e&&t.deps===null&&t.effects===null){var n=t,r=n.parent;r===null||r.f&P||(n.f^=P)}}else if((k==null?void 0:k.reaction)!==y||!(k!=null&&k.sources.includes(t))){var s=y.deps;t.rv<le&&(t.rv=le,$===null&&s!==null&&s[j]===t?j++:$===null?$=[t]:Z&&$.includes(t)||$.push(t))}return e&&Qt(n=t)&&Mr(n),At&&Mt.has(t)?Mt.get(t):t.v}function wi(t){var e=function(r){var s=ot;ot=new Set;var a,o=ot;try{if(rt(r),s!==null)for(a of ot)s.add(a)}finally{ot=s}return o}(()=>rt(t));for(var n of e)Rr(n,n.v)}function rt(t){var e=z;try{return z=!0,t()}finally{z=e}}const Oa=-7169;function W(t,e){t.f=t.f&Oa|e}function xi(t,e){var n={};for(var r in t)e.includes(r)||(n[r]=t[r]);return n}function $a(t){if(typeof t=="object"&&t&&!(t instanceof EventTarget)){if(ut in t)qe(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&ut in n&&qe(n)}}}function qe(t,e=new Set){if(!(typeof t!="object"||t===null||t instanceof EventTarget||e.has(t))){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{qe(t[r],e)}catch{}const n=nn(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Ar(n);for(let s in r){const a=r[s].get;if(a)try{a.call(t)}catch{}}}}}function Ta(t){return t.endsWith("capture")&&t!=="gotpointercapture"&&t!=="lostpointercapture"}const Na=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function Ca(t){return Na.includes(t)}const ja={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function Pa(t){return t=t.toLowerCase(),ja[t]??t}const Da=["touchstart","touchmove"];function La(t){return Da.includes(t)}function Ma(t,e){if(e){const n=document.body;t.autofocus=!0,Zr(()=>{document.activeElement===n&&t.focus()})}}let Wn=!1;function Ei(t,e,n,r=!0){for(var s of(r&&n(),e))t.addEventListener(s,n);ge(()=>{for(var a of e)t.removeEventListener(a,n)})}function os(t){var e=y,n=b;K(null),nt(null);try{return t()}finally{K(e),nt(n)}}function ki(t,e,n,r=n){t.addEventListener(e,()=>os(n));const s=t.__on_r;t.__on_r=s?()=>{s(),r(!0)}:()=>r(!0),Wn||(Wn=!0,document.addEventListener("reset",a=>{Promise.resolve().then(()=>{var o;if(!a.defaultPrevented)for(const c of a.target.elements)(o=c.__on_r)==null||o.call(c)})},{capture:!0}))}const is=new Set,We=new Set;function cs(t,e,n,r={}){function s(a){if(r.capture||Nt.call(e,a),!a.cancelBubble)return os(()=>n==null?void 0:n.call(this,a))}return t.startsWith("pointer")||t.startsWith("touch")||t==="wheel"?Zr(()=>{e.addEventListener(t,s,r)}):e.addEventListener(t,s,r),s}function Ii(t,e,n,r,s){var a={capture:r,passive:s},o=cs(t,e,n,a);(e===document.body||e===window||e===document||e instanceof HTMLMediaElement)&&ge(()=>{e.removeEventListener(t,o,a)})}function Ra(t){for(var e=0;e<t.length;e++)is.add(t[e]);for(var n of We)n(t)}function Nt(t){var E;var e=this,n=e.ownerDocument,r=t.type,s=((E=t.composedPath)==null?void 0:E.call(t))||[],a=s[0]||t.target,o=0,c=t.__root;if(c){var i=s.indexOf(c);if(i!==-1&&(e===document||e===window))return void(t.__root=e);var u=s.indexOf(e);if(u===-1)return;i<=u&&(o=i)}if((a=s[o]||t.target)!==e){ua(t,"currentTarget",{configurable:!0,get:()=>a||n});var l=y,f=b;K(null),nt(null);try{for(var p,h=[];a!==null;){var v=a.assignedSlot||a.parentNode||a.host||null;try{var d=a["__"+r];if(d!=null&&(!a.disabled||t.target===a))if(me(d)){var[m,..._]=d;m.apply(a,[t,..._])}else d.call(a,t)}catch(I){p?h.push(I):p=I}if(t.cancelBubble||v===e||v===null)break;a=v}if(p){for(let I of h)queueMicrotask(()=>{throw I});throw p}}finally{t.__root=e,delete t.currentTarget,K(l),nt(f)}}}function us(t){var e=document.createElement("template");return e.innerHTML=t.replaceAll("<!>","<!---->"),e.content}function Jt(t,e){var n=b;n.nodes_start===null&&(n.nodes_start=t,n.nodes_end=e)}function Ai(t,e){var n,r=!!(1&e),s=!!(2&e),a=!t.startsWith("<!>");return()=>{n===void 0&&(n=us(a?t:"<!>"+t),r||(n=xt(n)));var o=s||Fr?document.importNode(n,!0):n.cloneNode(!0);return r?Jt(xt(o),o.lastChild):Jt(o,o),o}}function Oi(t,e){return function(n,r,s="svg"){var a,o=`<${s}>${n.startsWith("<!>")?"<!>"+n:n}</${s}>`;return()=>{if(!a){var c=xt(us(o));a=xt(c)}var i=a.cloneNode(!0);return Jt(i,i),i}}(t,0,"svg")}function $i(t=""){var e=fn(t+"");return Jt(e,e),e}function Ti(){var t=document.createDocumentFragment(),e=document.createComment(""),n=fn();return t.append(e,n),Jt(e,n),t}function Ni(t,e){t!==null&&t.before(e)}let ze=!0;function Ci(t){ze=t}function ji(t,e){var n=e==null?"":typeof e=="object"?e+"":e;n!==(t.__t??(t.__t=t.nodeValue))&&(t.__t=n,t.nodeValue=n+"")}function Pi(t,e){return function(n,{target:r,anchor:s,props:a={},events:o,context:c,intro:i=!0}){(function(){if(Vn===void 0){Vn=window,ya=document,Fr=/Firefox/.test(navigator.userAgent);var h=Element.prototype,v=Node.prototype,d=Text.prototype;Vr=jt(v,"firstChild").get,Jr=jt(v,"nextSibling").get,Mn(h)&&(h.__click=void 0,h.__className=void 0,h.__attributes=null,h.__style=void 0,h.__e=void 0),Mn(d)&&(d.__t=void 0)}})();var u=new Set,l=h=>{for(var v=0;v<h.length;v++){var d=h[v];if(!u.has(d)){u.add(d);var m=La(d);r.addEventListener(d,Nt,{passive:m});var _=vt.get(d);_===void 0?(document.addEventListener(d,Nt,{passive:m}),vt.set(d,1)):vt.set(d,_+1)}}};l(ca(is)),We.add(l);var f=void 0,p=function(h){const v=at(It,h,!0);return(d={})=>new Promise(m=>{d.outro?Je(v,()=>{B(v),m(void 0)}):(B(v),m(void 0))})}(()=>{var h=s??r.appendChild(fn());return Rt(()=>{c&&(_a({}),g.c=c),o&&(a.$$events=o),ze=i,f=n(h,a)||{},ze=!0,c&&ga()}),()=>{var m;for(var v of u){r.removeEventListener(v,Nt);var d=vt.get(v);--d==0?(document.removeEventListener(v,Nt),vt.delete(v)):vt.set(v,d)}We.delete(l),h!==s&&((m=h.parentNode)==null||m.removeChild(h))}});return Be.set(f,p),f}(t,e)}const vt=new Map;let Be=new WeakMap;function Di(t,e){const n=Be.get(t);return n?(Be.delete(t),n(e)):Promise.resolve()}function Li(t,e,[n,r]=[0,0]){var s=t,a=null,o=null,c=N,i=!1;const u=(f,p=!0)=>{i=!0,l(p,f)},l=(f,p)=>{c!==(c=f)&&(c?(a?Jn(a):p&&(a=Rt(()=>p(s))),o&&Je(o,()=>{o=null})):(o?Jn(o):p&&(o=Rt(()=>p(s,[n+1,r]))),a&&Je(a,()=>{a=null})))};ye(()=>{i=!1,e(u),i||l(null,null)},n>0?cn:0)}function Ua(t,e){var n,r=void 0;ye(()=>{r!==(r=e())&&(n&&(B(n),n=null),r&&(n=Rt(()=>{zr(()=>r(t))})))})}function ls(t){var e,n,r="";if(typeof t=="string"||typeof t=="number")r+=t;else if(typeof t=="object")if(Array.isArray(t)){var s=t.length;for(e=0;e<s;e++)t[e]&&(n=ls(t[e]))&&(r&&(r+=" "),r+=n)}else for(n in t)t[n]&&(r&&(r+=" "),r+=n);return r}function Fa(t){return typeof t=="object"?function(){for(var e,n,r=0,s="",a=arguments.length;r<a;r++)(e=arguments[r])&&(n=ls(e))&&(s&&(s+=" "),s+=n);return s}(t):t??""}const zn=[...` 	
\r\f \v\uFEFF`];function Bn(t,e=!1){var n=e?" !important;":";",r="";for(var s in t){var a=t[s];a!=null&&a!==""&&(r+=" "+s+": "+a+n)}return r}function Te(t){return t[0]!=="-"||t[1]!=="-"?t.toLowerCase():t}function Va(t,e,n,r,s,a){var o=t.__className;if(o!==n||o===void 0){var c=function(l,f,p){var h=l==null?"":""+l;if(f&&(h=h?h+" "+f:f),p){for(var v in p)if(p[v])h=h?h+" "+v:v;else if(h.length)for(var d=v.length,m=0;(m=h.indexOf(v,m))>=0;){var _=m+d;m!==0&&!zn.includes(h[m-1])||_!==h.length&&!zn.includes(h[_])?m=_:h=(m===0?"":h.substring(0,m))+h.substring(_+1)}}return h===""?null:h}(n,r,a);c==null?t.removeAttribute("class"):e?t.className=c:t.setAttribute("class",c),t.__className=n}else if(a&&s!==a)for(var i in a){var u=!!a[i];s!=null&&u===!!s[i]||t.classList.toggle(i,u)}return a}function Ne(t,e={},n,r){for(var s in n){var a=n[s];e[s]!==a&&(n[s]==null?t.style.removeProperty(s):t.style.setProperty(s,a,r))}}function Ja(t,e,n,r){if(t.__style!==e){var s=function(a,o){if(o){var c,i,u="";if(Array.isArray(o)?(c=o[0],i=o[1]):c=o,a){a=String(a).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var l=!1,f=0,p=!1,h=[];c&&h.push(...Object.keys(c).map(Te)),i&&h.push(...Object.keys(i).map(Te));var v=0,d=-1;const I=a.length;for(var m=0;m<I;m++){var _=a[m];if(p?_==="/"&&a[m-1]==="*"&&(p=!1):l?l===_&&(l=!1):_==="/"&&a[m+1]==="*"?p=!0:_==='"'||_==="'"?l=_:_==="("?f++:_===")"&&f--,!p&&l===!1&&f===0){if(_===":"&&d===-1)d=m;else if(_===";"||m===I-1){if(d!==-1){var E=Te(a.substring(v,d).trim());h.includes(E)||(_!==";"&&m++,u+=" "+a.substring(v,m).trim()+";")}v=m+1,d=-1}}}}return c&&(u+=Bn(c)),i&&(u+=Bn(i,!0)),(u=u.trim())===""?null:u}return a==null?null:String(a)}(e,r);s==null?t.removeAttribute("style"):t.style.cssText=s,t.__style=e}else r&&(Array.isArray(r)?(Ne(t,n==null?void 0:n[0],r[0]),Ne(t,n==null?void 0:n[1],r[1],"important")):Ne(t,n,r));return r}function Ce(t,e,n=!1){if(t.multiple){if(e==null)return;if(!me(e))return void console.warn("https://svelte.dev/e/select_multiple_invalid_value");for(var r of t.options)r.selected=e.includes(Kn(r))}else{for(r of t.options){var s=Kn(r);if(a=s,o=e,Object.is(Fn(a),Fn(o)))return void(r.selected=!0)}var a,o;n&&e===void 0||(t.selectedIndex=-1)}}function Kn(t){return"__value"in t?t.__value:t.value}const Ot=Symbol("class"),$t=Symbol("style"),fs=Symbol("is custom element"),ps=Symbol("is html");function Mi(t,e){var n=we(t);n.value!==(n.value=e??void 0)&&(t.value!==e||e===0&&t.nodeName==="PROGRESS")&&(t.value=e??"")}function Ri(t,e){var n=we(t);n.checked!==(n.checked=e??void 0)&&(t.checked=e)}function qa(t,e){e?t.hasAttribute("selected")||t.setAttribute("selected",""):t.removeAttribute("selected")}function Yn(t,e,n,r){var s=we(t);s[e]!==(s[e]=n)&&(e==="loading"&&(t[ha]=n),n==null?t.removeAttribute(e):typeof n!="string"&&ds(t).includes(e)?t[e]=n:t.setAttribute(e,n))}function Ui(t,e,n=[],r,s=!1,a=Ht){const o=n.map(a);var c=void 0,i={},u=t.nodeName==="SELECT",l=!1;if(ye(()=>{var p=e(...o.map(J)),h=function(d,m,_,E,I=!1){var Y=we(d),dt=Y[fs],xe=!Y[ps],L=m||{},dn=d.tagName==="OPTION";for(var hn in m)hn in _||(_[hn]=null);_.class?_.class=Fa(_.class):(E||_[Ot])&&(_.class=null),_[$t]&&(_.style??(_.style=null));var Ss=ds(d);for(const S in _){let w=_[S];if(dn&&S==="value"&&w==null)d.value=d.__value="",L[S]=w;else if(S!=="class")if(S!=="style"){var vn=L[S];if(w!==vn||w===void 0&&d.hasAttribute(S)){L[S]=w;var mn=S[0]+S[1];if(mn!=="$$")if(mn==="on"){const C={},ht="$$"+S;let O=S.slice(2);var Ee=Ca(O);if(Ta(O)&&(O=O.slice(0,-7),C.capture=!0),!Ee&&vn){if(w!=null)continue;d.removeEventListener(O,L[ht],C),L[ht]=null}if(w!=null)if(Ee)d[`__${O}`]=w,Ra([O]);else{let ws=function(xs){L[S].call(this,xs)};L[ht]=cs(O,d,ws,C)}else Ee&&(d[`__${O}`]=void 0)}else if(S==="style")Yn(d,S,w);else if(S==="autofocus")Ma(d,!!w);else if(dt||S!=="__value"&&(S!=="value"||w==null))if(S==="selected"&&dn)qa(d,w);else{var M=S;xe||(M=Pa(M));var _n=M==="defaultValue"||M==="defaultChecked";if(w!=null||dt||_n)_n||Ss.includes(M)&&(dt||typeof w!="string")?d[M]=w:typeof w!="function"&&Yn(d,M,w);else if(Y[S]=null,M==="value"||M==="checked"){let C=d;const ht=m===void 0;if(M==="value"){let O=C.defaultValue;C.removeAttribute(M),C.defaultValue=O,C.value=C.__value=ht?O:null}else{let O=C.defaultChecked;C.removeAttribute(M),C.defaultChecked=O,C.checked=!!ht&&O}}else d.removeAttribute(S)}else d.value=d.__value=w}}else Ja(d,w,m==null?void 0:m[$t],_[$t]),L[S]=w,L[$t]=_[$t];else Va(d,d.namespaceURI==="http://www.w3.org/1999/xhtml",w,E,m==null?void 0:m[Ot],_[Ot]),L[S]=w,L[Ot]=_[Ot]}return L}(t,c,p,r,s);l&&u&&"value"in p&&Ce(t,p.value);for(let d of Object.getOwnPropertySymbols(i))p[d]||B(i[d]);for(let d of Object.getOwnPropertySymbols(p)){var v=p[d];d.description!=="@attach"||c&&v===c[d]||(i[d]&&B(i[d]),i[d]=Rt(()=>Ua(t,()=>v))),h[d]=v}c=h}),u){var f=t;zr(()=>{Ce(f,c.value,!0),function(p){var h=new MutationObserver(()=>{Ce(p,p.__value)});h.observe(p,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),ge(()=>{h.disconnect()})}(f)})}l=!0}function we(t){return t.__attributes??(t.__attributes={[fs]:t.nodeName.includes("-"),[ps]:t.namespaceURI===ma})}var Gn=new Map;function ds(t){var e,n=Gn.get(t.nodeName);if(n)return n;Gn.set(t.nodeName,n=[]);for(var r=t,s=Element.prototype;s!==r;){for(var a in e=Ar(r))e[a].set&&n.push(a);r=nn(r)}return n}function Fi(t=!1){const e=g,n=e.l.u;if(!n)return;let r=()=>$a(e.s);if(t){let a=0,o={};const c=Ht(()=>{let i=!1;const u=e.s;for(const l in u)u[l]!==o[l]&&(o[l]=u[l],i=!0);return i&&a++,a});r=()=>J(c)}var s;n.b.length&&(s=()=>{Hn(e,r),Lt(n.b)},qr(),at(Kt|un,s,!0)),Ve(()=>{const a=rt(()=>n.m.map(pa));return()=>{for(const o of a)typeof o=="function"&&o()}}),n.a.length&&Ve(()=>{Hn(e,r),Lt(n.a)})}function Hn(t,e){if(t.l.s)for(const n of t.l.s)J(n);e()}function hs(t,e,n){if(t==null)return e(void 0),n&&n(void 0),yt;const r=rt(()=>t.subscribe(e,n));return r.unsubscribe?()=>r.unsubscribe():r}const mt=[];function Wa(t,e){return{subscribe:vs(t,e).subscribe}}function vs(t,e=yt){let n=null;const r=new Set;function s(o){if(Cr(t,o)&&(t=o,n)){const c=!mt.length;for(const i of r)i[1](),mt.push(i,t);if(c){for(let i=0;i<mt.length;i+=2)mt[i][0](mt[i+1]);mt.length=0}}}function a(o){s(o(t))}return{set:s,update:a,subscribe:function(o,c=yt){const i=[o,c];return r.add(i),r.size===1&&(n=e(s,a)||yt),o(t),()=>{r.delete(i),r.size===0&&n&&(n(),n=null)}}}}function Vi(t,e,n){const r=!Array.isArray(t),s=r?[t]:t;if(!s.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const a=e.length<2;return Wa(n,(o,c)=>{let i=!1;const u=[];let l=0,f=yt;const p=()=>{if(l)return;f();const v=e(r?u[0]:u,o,c);a?o(v):f=typeof v=="function"?v:yt},h=s.map((v,d)=>hs(v,m=>{u[d]=m,l&=~(1<<d),i&&p()},()=>{l|=1<<d}));return i=!0,p(),function(){Lt(h),f(),i=!1}})}function Ji(t){return{subscribe:t.subscribe.bind(t)}}function qi(t){let e;return hs(t,n=>e=n)(),e}function za(t){g===null&&Gt(),Yt&&g.l!==null?ms(g).m.push(t):Ve(()=>{const e=rt(t);if(typeof e=="function")return e})}function Wi(t){g===null&&Gt(),za(()=>()=>rt(t))}function zi(){const t=g;return t===null&&Gt(),(e,n,r)=>{var a;const s=(a=t.s.$$events)==null?void 0:a[e];if(s){const o=me(s)?s.slice():[s],c=function(i,u,{bubbles:l=!1,cancelable:f=!1}={}){return new CustomEvent(i,{detail:u,bubbles:l,cancelable:f})}(e,n,r);for(const i of o)i.call(t.x,c);return!c.defaultPrevented}return!0}}function Bi(t){g===null&&Gt(),g.l===null&&function(e){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}(),ms(g).b.push(t)}function ms(t){var e=t.l;return e.u??(e.u={a:[],b:[],m:[]})}let Ba=document.documentElement;function pt(){return Ba??document.documentElement}var _s=(t=>(t.light="light",t.dark="dark",t))(_s||{}),gs=(t=>(t.regular="regular",t.highContrast="high-contrast",t))(gs||{});const pe="data-augment-theme-category",de="data-augment-theme-intensity";function ys(){const t=pt().getAttribute(pe);if(t&&Object.values(_s).includes(t))return t}function Ki(t){t===void 0?pt().removeAttribute(pe):pt().setAttribute(pe,t)}function bs(){const t=pt().getAttribute(de);if(t&&Object.values(gs).includes(t))return t}function Yi(t){t===void 0?pt().removeAttribute(de):pt().setAttribute(de,t)}const Qn=vs(void 0);function Ka(t){const e=new MutationObserver(n=>{for(const r of n)if(r.type==="attributes"){t(ys(),bs());break}});return e.observe(pt(),{attributeFilter:[pe,de],attributes:!0}),e}Ka((t,e)=>{Qn.update(()=>({category:t,intensity:e}))}),Qn.update(()=>({category:ys(),intensity:bs()}));var Xn;typeof window<"u"&&((Xn=window.__svelte??(window.__svelte={})).v??(Xn.v=new Set)).add("5"),Yt=!0;export{zi as $,xi as A,$a as B,J as C,yi as D,cn as E,Fi as F,Ai as G,Ot as H,vi as I,Li as J,mi as K,ga as L,Ti as M,$i as N,bi as O,ji as P,li as Q,ii as R,ci as S,Wi as T,N as U,Ka as V,ys as W,_s as X,Va as Y,Ja as Z,Yn as _,Ui as a,q as a$,zr as a0,Ii as a1,B as a2,ai as a3,Jt as a4,fn as a5,b as a6,Ci as a7,za as a8,Vn as a9,_i as aA,Sa as aB,Ho as aC,pn as aD,gs as aE,Ki as aF,Yi as aG,Mo as aH,Wo as aI,Cr as aJ,Vi as aK,ba as aL,us as aM,xt as aN,Ji as aO,Si as aP,Fa as aQ,jt as aR,ge as aS,oi as aT,nn as aU,la as aV,Wa as aW,wi as aX,A as aY,x as aZ,Xs as a_,yt as aa,ze as ab,sn as ac,Or as ad,si as ae,os as af,Uo as ag,y as ah,ni as ai,ri as aj,pi as ak,Mi as al,ui as am,Jo as an,Ri as ao,Pi as ap,Qn as aq,qi as ar,Yo as as,ca as at,me as au,wt as av,Go as aw,Bo as ax,Ko as ay,Hr as az,Ni as b,qt as b$,Ys as b0,X as b1,T as b2,ft as b3,Xe as b4,ie as b5,oe as b6,Ae as b7,Bt as b8,Dt as b9,lo as bA,Xa as bB,Do as bC,$o as bD,Co as bE,Po as bF,mo as bG,Oo as bH,Ze as bI,As as bJ,ae as bK,Gs as bL,tr as bM,Ye as bN,To as bO,No as bP,jo as bQ,Ct as bR,Et as bS,zt as bT,xo as bU,bo as bV,Ws as bW,zs as bX,ao as bY,io as bZ,ro as b_,_t as ba,Ks as bb,V as bc,ee as bd,_o as be,Eo as bf,Ro as bg,er as bh,ir as bi,wo as bj,or as bk,it as bl,uo as bm,vo as bn,Zs as bo,je as bp,et as bq,br,Ms as bs,po as bt,Ge as bu,ho as bv,Fs as bw,Us as bx,tn as by,Ao as bz,ye as c,gn as c0,so as c1,yn as c2,Io as c3,yo as c4,Za as c5,hr as c6,De as c7,Le as c8,Is as c9,bt as cA,So as cB,re as cC,gt as cD,ut as cE,ua as cF,hs as cG,zo as cH,Zo as cI,Tt as cJ,Ht as cK,ti as cL,Yt as cM,Xo as cN,Qo as cO,ei as cP,di as cQ,qo as cR,ya as cS,Ve as cT,Ei as cU,Di as cV,Bi as cW,Vo as cX,Q as cY,tt as ca,to as cb,js as cc,He as cd,co as ce,Ga as cf,Ha as cg,Qa as ch,Os as ci,ra as cj,oo as ck,xr as cl,fo as cm,lr as cn,ur as co,Ts as cp,Lo as cq,eo as cr,te as cs,Qs as ct,Hs as cu,no as cv,ko as cw,Rs as cx,go as cy,cr as cz,Fo as d,Rr as e,Oi as f,nt as g,K as h,_e as i,Un as j,g as k,Rt as l,fi as m,Aa as n,hi as o,Je as p,Zr as q,Jn as r,ln as s,ki as t,rt as u,Br as v,vs as w,_a as x,gi as y,F as z};
