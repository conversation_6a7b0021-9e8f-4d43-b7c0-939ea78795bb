# Change Log

该版本为Augment v0.531.0的网络增强修改版，适用于中国大陆网络环境。

## [Enhanced] - 2025-08-15

### Added
- 代理服务器配置支持，解决网络连接问题
- 网络优化配置模块，包含超时重试和连接复用
- HTTP Keep-Alive连接复用功能，提升网络效率
- 并发请求控制，防止网络过载
- 请求队列管理系统，优化请求调度
- 本地缓存功能，减少重复网络请求
- 请求节流机制，避免频繁API调用

### Changed
- 代码完成配置增强，添加重试和缓存机制
- Next Edit后台建议默认启用，配合代理服务器提供流畅编码体验
- 添加建议延迟配置，减少频繁请求
- 启用IntelliSense快速建议和弹窗补全
- 优化服务器连接配置，提升网络稳定性

### Enhanced
- 网络连接稳定性显著提升，特别针对中国大陆网络环境进行优化
- 代码完成响应时间更加稳定，减少因网络波动导致的功能异常
- 插件整体性能优化，降低CPU和网络资源占用
- 用户体验改善，减少等待时间和请求失败情况
- 通过代理服务器访问，解决网络连接问题
- 移除遥测内容，避免遥测数据上传

### Technical Details
- 新增proxyConfig配置对象，支持代理服务器配置
- 新增networkOptimization配置对象，包含7个网络相关参数，默认值自动生效
- completions配置扩展，新增6个性能优化选项，包含缓存和重试机制
- 建议延迟机制实现，Next Edit建议延迟2000ms
- 请求节流算法集成，自动启用智能控制API调用频率
- 所有优化配置采用自动生效模式，用户安装即享受优化效果
- 网络超时参数优化：连接30s，请求60s，重试5次间隔2s
- 代码完成参数优化：保持原生800ms/1600ms，启用缓存和重试
- 代理服务器配置：自动配置代理参数，提升网络连接质量

---

**修改作者**: dabaotongxue
**基于版本**: vscode-augment-0.531.0