var un=Object.defineProperty;var ln=(n,t,e)=>t in n?un(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var Re=(n,t,e)=>ln(n,typeof t!="symbol"?t+"":t,e);import{x as zt,F as Wt,G as C,K as S,o as h,O as dt,P as mt,b as _,J as X,C as i,Q as U,u as w,B as P,L as Rt,T as Ze,m as D,z as M,y as L,D as de,M as Ve,I as Dt,N as ke,Y as Ne,ak as ht,a1 as Pt,a9 as Ke,am as Ae,ap as dn}from"./legacy-YP6Kq8lu.js";import{p as H,i as Oe,a as mn,T as hn}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";import{e as At,i as re,h as Ot,W as ft}from"./host-BNehKqab.js";import{t as kt,d as fn,m as tn,a as Ee,b as Fe,F as at}from"./feedback-rating-RDPz08ah.js";import{C as gn}from"./CopyButton-CuvLtN0u.js";import{B as Me}from"./ButtonAugment-DkEdzEZO.js";import{c as pn,S as me,M as vn}from"./index-hRm--fCg.js";import{e as bn,R as Zt}from"./toggleHighContrast-Cb9MCs64.js";import{C as ve}from"./next-edit-types-904A5ehg.js";import{T as wn}from"./TextAreaAugment-Bs79KMH3.js";import{C as yn}from"./CardAugment-D6yVifBE.js";import{B as xn,I as je}from"./IconButtonAugment-CbpcmeFk.js";import{o as Ye}from"./keypress-DD1aQVr0.js";import{M as Ie}from"./MaterialIcon-j5PxZ6X_.js";import"./copy-ChvqXPeP.js";import"./index-4vhrZf9p.js";import"./preload-helper-Dv6uf1Os.js";import"./input-C2nR_fsN.js";import"./BaseTextInput-Br9yLRnx.js";import"./event-modifiers-Bz4QCcZc.js";function Et(n,t){return n instanceof Date?new n.constructor(t):new Date(t)}let kn={};function fe(){return kn}function ie(n,t){var d,o,c,u;const e=fe(),r=(t==null?void 0:t.weekStartsOn)??((o=(d=t==null?void 0:t.locale)==null?void 0:d.options)==null?void 0:o.weekStartsOn)??e.weekStartsOn??((u=(c=e.locale)==null?void 0:c.options)==null?void 0:u.weekStartsOn)??0,a=kt(n),s=a.getDay(),y=(s<r?7:0)+s-r;return a.setDate(a.getDate()-y),a.setHours(0,0,0,0),a}function he(n){return ie(n,{weekStartsOn:1})}function en(n){const t=kt(n),e=t.getFullYear(),r=Et(n,0);r.setFullYear(e+1,0,4),r.setHours(0,0,0,0);const a=he(r),s=Et(n,0);s.setFullYear(e,0,4),s.setHours(0,0,0,0);const y=he(s);return t.getTime()>=a.getTime()?e+1:t.getTime()>=y.getTime()?e:e-1}function Nn(n){if(t=n,!(t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"||typeof n=="number"))return!1;var t;const e=kt(n);return!isNaN(Number(e))}const Mn={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function be(n){return(t={})=>{const e=t.width?String(t.width):n.defaultWidth;return n.formats[e]||n.formats[n.defaultWidth]}}const _n={date:be({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:be({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:be({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Tn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function Vt(n){return(t,e)=>{let r;if((e!=null&&e.context?String(e.context):"standalone")==="formatting"&&n.formattingValues){const a=n.defaultFormattingWidth||n.defaultWidth,s=e!=null&&e.width?String(e.width):a;r=n.formattingValues[s]||n.formattingValues[a]}else{const a=n.defaultWidth,s=e!=null&&e.width?String(e.width):n.defaultWidth;r=n.values[s]||n.values[a]}return r[n.argumentCallback?n.argumentCallback(t):t]}}const Pn={ordinalNumber:(n,t)=>{const e=Number(n),r=e%100;if(r>20||r<10)switch(r%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},era:Vt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:Vt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:n=>n-1}),month:Vt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:Vt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:Vt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function Kt(n){return(t,e={})=>{const r=e.width,a=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],s=t.match(a);if(!s)return null;const y=s[0],d=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],o=Array.isArray(d)?function(u,g){for(let x=0;x<u.length;x++)if(g(u[x]))return x}(d,u=>u.test(y)):function(u,g){for(const x in u)if(Object.prototype.hasOwnProperty.call(u,x)&&g(u[x]))return x}(d,u=>u.test(y));let c;return c=n.valueCallback?n.valueCallback(o):o,c=e.valueCallback?e.valueCallback(c):c,{value:c,rest:t.slice(y.length)}}}const Dn={ordinalNumber:(te={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:n=>parseInt(n,10)},(n,t={})=>{const e=n.match(te.matchPattern);if(!e)return null;const r=e[0],a=n.match(te.parsePattern);if(!a)return null;let s=te.valueCallback?te.valueCallback(a[0]):a[0];return s=t.valueCallback?t.valueCallback(s):s,{value:s,rest:n.slice(r.length)}}),era:Kt({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Kt({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:n=>n+1}),month:Kt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Kt({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Kt({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})};var te;const Cn={code:"en-US",formatDistance:(n,t,e)=>{let r;const a=Mn[n];return r=typeof a=="string"?a:t===1?a.one:a.other.replace("{{count}}",t.toString()),e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"in "+r:r+" ago":r},formatLong:_n,formatRelative:(n,t,e,r)=>Tn[n],localize:Pn,match:Dn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Sn(n){const t=kt(n);return fn(t,function(r){const a=kt(r),s=Et(r,0);return s.setFullYear(a.getFullYear(),0,1),s.setHours(0,0,0,0),s}(t))+1}function qn(n){const t=kt(n),e=+he(t)-+function(r){const a=en(r),s=Et(r,0);return s.setFullYear(a,0,4),s.setHours(0,0,0,0),he(s)}(t);return Math.round(e/tn)+1}function nn(n,t){var u,g,x,b;const e=kt(n),r=e.getFullYear(),a=fe(),s=(t==null?void 0:t.firstWeekContainsDate)??((g=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:g.firstWeekContainsDate)??a.firstWeekContainsDate??((b=(x=a.locale)==null?void 0:x.options)==null?void 0:b.firstWeekContainsDate)??1,y=Et(n,0);y.setFullYear(r+1,0,s),y.setHours(0,0,0,0);const d=ie(y,t),o=Et(n,0);o.setFullYear(r,0,s),o.setHours(0,0,0,0);const c=ie(o,t);return e.getTime()>=d.getTime()?r+1:e.getTime()>=c.getTime()?r:r-1}function $n(n,t){const e=kt(n),r=+ie(e,t)-+function(a,s){var u,g,x,b;const y=fe(),d=(s==null?void 0:s.firstWeekContainsDate)??((g=(u=s==null?void 0:s.locale)==null?void 0:u.options)==null?void 0:g.firstWeekContainsDate)??y.firstWeekContainsDate??((b=(x=y.locale)==null?void 0:x.options)==null?void 0:b.firstWeekContainsDate)??1,o=nn(a,s),c=Et(a,0);return c.setFullYear(o,0,d),c.setHours(0,0,0,0),ie(c,s)}(e,t);return Math.round(r/tn)+1}function q(n,t){return(n<0?"-":"")+Math.abs(n).toString().padStart(t,"0")}const Tt={y(n,t){const e=n.getFullYear(),r=e>0?e:1-e;return q(t==="yy"?r%100:r,t.length)},M(n,t){const e=n.getMonth();return t==="M"?String(e+1):q(e+1,2)},d:(n,t)=>q(n.getDate(),t.length),a(n,t){const e=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];default:return e==="am"?"a.m.":"p.m."}},h:(n,t)=>q(n.getHours()%12||12,t.length),H:(n,t)=>q(n.getHours(),t.length),m:(n,t)=>q(n.getMinutes(),t.length),s:(n,t)=>q(n.getSeconds(),t.length),S(n,t){const e=t.length,r=n.getMilliseconds();return q(Math.trunc(r*Math.pow(10,e-3)),t.length)}},zn="midnight",Wn="noon",Rn="morning",An="afternoon",On="evening",En="night",He={G:function(n,t,e){const r=n.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return e.era(r,{width:"abbreviated"});case"GGGGG":return e.era(r,{width:"narrow"});default:return e.era(r,{width:"wide"})}},y:function(n,t,e){if(t==="yo"){const r=n.getFullYear(),a=r>0?r:1-r;return e.ordinalNumber(a,{unit:"year"})}return Tt.y(n,t)},Y:function(n,t,e,r){const a=nn(n,r),s=a>0?a:1-a;return t==="YY"?q(s%100,2):t==="Yo"?e.ordinalNumber(s,{unit:"year"}):q(s,t.length)},R:function(n,t){return q(en(n),t.length)},u:function(n,t){return q(n.getFullYear(),t.length)},Q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return q(r,2);case"Qo":return e.ordinalNumber(r,{unit:"quarter"});case"QQQ":return e.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(r,{width:"narrow",context:"formatting"});default:return e.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return q(r,2);case"qo":return e.ordinalNumber(r,{unit:"quarter"});case"qqq":return e.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(r,{width:"narrow",context:"standalone"});default:return e.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,t,e){const r=n.getMonth();switch(t){case"M":case"MM":return Tt.M(n,t);case"Mo":return e.ordinalNumber(r+1,{unit:"month"});case"MMM":return e.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(r,{width:"narrow",context:"formatting"});default:return e.month(r,{width:"wide",context:"formatting"})}},L:function(n,t,e){const r=n.getMonth();switch(t){case"L":return String(r+1);case"LL":return q(r+1,2);case"Lo":return e.ordinalNumber(r+1,{unit:"month"});case"LLL":return e.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(r,{width:"narrow",context:"standalone"});default:return e.month(r,{width:"wide",context:"standalone"})}},w:function(n,t,e,r){const a=$n(n,r);return t==="wo"?e.ordinalNumber(a,{unit:"week"}):q(a,t.length)},I:function(n,t,e){const r=qn(n);return t==="Io"?e.ordinalNumber(r,{unit:"week"}):q(r,t.length)},d:function(n,t,e){return t==="do"?e.ordinalNumber(n.getDate(),{unit:"date"}):Tt.d(n,t)},D:function(n,t,e){const r=Sn(n);return t==="Do"?e.ordinalNumber(r,{unit:"dayOfYear"}):q(r,t.length)},E:function(n,t,e){const r=n.getDay();switch(t){case"E":case"EE":case"EEE":return e.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(r,{width:"short",context:"formatting"});default:return e.day(r,{width:"wide",context:"formatting"})}},e:function(n,t,e,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return q(s,2);case"eo":return e.ordinalNumber(s,{unit:"day"});case"eee":return e.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(a,{width:"short",context:"formatting"});default:return e.day(a,{width:"wide",context:"formatting"})}},c:function(n,t,e,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return q(s,t.length);case"co":return e.ordinalNumber(s,{unit:"day"});case"ccc":return e.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(a,{width:"narrow",context:"standalone"});case"cccccc":return e.day(a,{width:"short",context:"standalone"});default:return e.day(a,{width:"wide",context:"standalone"})}},i:function(n,t,e){const r=n.getDay(),a=r===0?7:r;switch(t){case"i":return String(a);case"ii":return q(a,t.length);case"io":return e.ordinalNumber(a,{unit:"day"});case"iii":return e.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(r,{width:"short",context:"formatting"});default:return e.day(r,{width:"wide",context:"formatting"})}},a:function(n,t,e){const r=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(r,{width:"narrow",context:"formatting"});default:return e.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(n,t,e){const r=n.getHours();let a;switch(a=r===12?Wn:r===0?zn:r/12>=1?"pm":"am",t){case"b":case"bb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(a,{width:"narrow",context:"formatting"});default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,t,e){const r=n.getHours();let a;switch(a=r>=17?On:r>=12?An:r>=4?Rn:En,t){case"B":case"BB":case"BBB":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(a,{width:"narrow",context:"formatting"});default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,t,e){if(t==="ho"){let r=n.getHours()%12;return r===0&&(r=12),e.ordinalNumber(r,{unit:"hour"})}return Tt.h(n,t)},H:function(n,t,e){return t==="Ho"?e.ordinalNumber(n.getHours(),{unit:"hour"}):Tt.H(n,t)},K:function(n,t,e){const r=n.getHours()%12;return t==="Ko"?e.ordinalNumber(r,{unit:"hour"}):q(r,t.length)},k:function(n,t,e){let r=n.getHours();return r===0&&(r=24),t==="ko"?e.ordinalNumber(r,{unit:"hour"}):q(r,t.length)},m:function(n,t,e){return t==="mo"?e.ordinalNumber(n.getMinutes(),{unit:"minute"}):Tt.m(n,t)},s:function(n,t,e){return t==="so"?e.ordinalNumber(n.getSeconds(),{unit:"second"}):Tt.s(n,t)},S:function(n,t){return Tt.S(n,t)},X:function(n,t,e){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Le(r);case"XXXX":case"XX":return $t(r);default:return $t(r,":")}},x:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"x":return Le(r);case"xxxx":case"xx":return $t(r);default:return $t(r,":")}},O:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Be(r,":");default:return"GMT"+$t(r,":")}},z:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Be(r,":");default:return"GMT"+$t(r,":")}},t:function(n,t,e){return q(Math.trunc(n.getTime()/1e3),t.length)},T:function(n,t,e){return q(n.getTime(),t.length)}};function Be(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),a=Math.trunc(r/60),s=r%60;return s===0?e+String(a):e+String(a)+t+q(s,2)}function Le(n,t){return n%60==0?(n>0?"-":"+")+q(Math.abs(n)/60,2):$t(n,t)}function $t(n,t=""){const e=n>0?"-":"+",r=Math.abs(n);return e+q(Math.trunc(r/60),2)+t+q(r%60,2)}const Qe=(n,t)=>{switch(n){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},Ge=(n,t)=>{switch(n){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},Fn={p:Ge,P:(n,t)=>{const e=n.match(/(P+)(p+)?/)||[],r=e[1],a=e[2];if(!a)return Qe(n,t);let s;switch(r){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;default:s=t.dateTime({width:"full"})}return s.replace("{{date}}",Qe(r,t)).replace("{{time}}",Ge(a,t))}},jn=/^D+$/,Yn=/^Y+$/,In=["D","DD","YY","YYYY"],Hn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Bn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ln=/^'([^]*?)'?$/,Qn=/''/g,Gn=/[a-zA-Z]/;function Xe(n,t,e){var u,g,x,b,R,N,T,l;const r=fe(),a=(e==null?void 0:e.locale)??r.locale??Cn,s=(e==null?void 0:e.firstWeekContainsDate)??((g=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:g.firstWeekContainsDate)??r.firstWeekContainsDate??((b=(x=r.locale)==null?void 0:x.options)==null?void 0:b.firstWeekContainsDate)??1,y=(e==null?void 0:e.weekStartsOn)??((N=(R=e==null?void 0:e.locale)==null?void 0:R.options)==null?void 0:N.weekStartsOn)??r.weekStartsOn??((l=(T=r.locale)==null?void 0:T.options)==null?void 0:l.weekStartsOn)??0,d=kt(n);if(!Nn(d))throw new RangeError("Invalid time value");let o=t.match(Bn).map(f=>{const k=f[0];return k==="p"||k==="P"?(0,Fn[k])(f,a.formatLong):f}).join("").match(Hn).map(f=>{if(f==="''")return{isToken:!1,value:"'"};const k=f[0];if(k==="'")return{isToken:!1,value:Xn(f)};if(He[k])return{isToken:!0,value:f};if(k.match(Gn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+k+"`");return{isToken:!1,value:f}});a.localize.preprocessor&&(o=a.localize.preprocessor(d,o));const c={firstWeekContainsDate:s,weekStartsOn:y,locale:a};return o.map(f=>{if(!f.isToken)return f.value;const k=f.value;return(!(e!=null&&e.useAdditionalWeekYearTokens)&&function(m){return Yn.test(m)}(k)||!(e!=null&&e.useAdditionalDayOfYearTokens)&&function(m){return jn.test(m)}(k))&&function(m,O,z){const E=function(J,tt,ot){const gt=J[0]==="Y"?"years":"days of the month";return`Use \`${J.toLowerCase()}\` instead of \`${J}\` (in \`${tt}\`) for formatting ${gt} to the input \`${ot}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(m,O,z);if(console.warn(E),In.includes(m))throw new RangeError(E)}(k,t,String(n)),(0,He[k[0]])(d,k,a.localize,c)}).join("")}function Xn(n){const t=n.match(Ln);return t?t[1].replace(Qn,"'"):n}function we(n,t){const e=function(d){const o={},c=d.split(ue.dateTimeDelimiter);let u;if(c.length>2)return o;if(/:/.test(c[0])?u=c[0]:(o.date=c[0],u=c[1],ue.timeZoneDelimiter.test(o.date)&&(o.date=d.split(ue.timeZoneDelimiter)[0],u=d.substr(o.date.length,d.length))),u){const g=ue.timezone.exec(u);g?(o.time=u.replace(g[1],""),o.timezone=g[1]):o.time=u}return o}(n);let r;if(e.date){const d=function(o,c){const u=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+c)+"})|(\\d{2}|[+-]\\d{"+(2+c)+"})$)"),g=o.match(u);if(!g)return{year:NaN,restDateString:""};const x=g[1]?parseInt(g[1]):null,b=g[2]?parseInt(g[2]):null;return{year:b===null?x:100*b,restDateString:o.slice((g[1]||g[2]).length)}}(e.date,2);r=function(o,c){if(c===null)return new Date(NaN);const u=o.match(Un);if(!u)return new Date(NaN);const g=!!u[4],x=ee(u[1]),b=ee(u[2])-1,R=ee(u[3]),N=ee(u[4]),T=ee(u[5])-1;if(g)return function(l,f,k){return f>=1&&f<=53&&k>=0&&k<=6}(0,N,T)?function(l,f,k){const m=new Date(0);m.setUTCFullYear(l,0,4);const O=m.getUTCDay()||7,z=7*(f-1)+k+1-O;return m.setUTCDate(m.getUTCDate()+z),m}(c,N,T):new Date(NaN);{const l=new Date(0);return function(f,k,m){return k>=0&&k<=11&&m>=1&&m<=(Vn[k]||(Ue(f)?29:28))}(c,b,R)&&function(f,k){return k>=1&&k<=(Ue(f)?366:365)}(c,x)?(l.setUTCFullYear(c,b,Math.max(x,R)),l):new Date(NaN)}}(d.restDateString,d.year)}if(!r||isNaN(r.getTime()))return new Date(NaN);const a=r.getTime();let s,y=0;if(e.time&&(y=function(d){const o=d.match(Jn);if(!o)return NaN;const c=ye(o[1]),u=ye(o[2]),g=ye(o[3]);return function(x,b,R){return x===24?b===0&&R===0:R>=0&&R<60&&b>=0&&b<60&&x>=0&&x<25}(c,u,g)?c*Ee+u*Fe+1e3*g:NaN}(e.time),isNaN(y)))return new Date(NaN);if(!e.timezone){const d=new Date(a+y),o=new Date(0);return o.setFullYear(d.getUTCFullYear(),d.getUTCMonth(),d.getUTCDate()),o.setHours(d.getUTCHours(),d.getUTCMinutes(),d.getUTCSeconds(),d.getUTCMilliseconds()),o}return s=function(d){if(d==="Z")return 0;const o=d.match(Zn);if(!o)return 0;const c=o[1]==="+"?-1:1,u=parseInt(o[2]),g=o[3]&&parseInt(o[3])||0;return function(x,b){return b>=0&&b<=59}(0,g)?c*(u*Ee+g*Fe):NaN}(e.timezone),isNaN(s)?new Date(NaN):new Date(a+y+s)}const ue={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Un=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Jn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Zn=/^([+-])(\d{2})(?::?(\d{2}))?$/;function ee(n){return n?parseInt(n):1}function ye(n){return n&&parseFloat(n.replace(",","."))||0}const Vn=[31,null,31,30,31,30,31,31,30,31,30,31];function Ue(n){return n%400==0||n%4==0&&n%100!=0}var Kn=C('<span slot="text" class="c-history-header--ellipsis svelte-8btr94"> </span>'),tr=C('<span class="c-history-header--ellipsis-left svelte-8btr94"> </span>'),er=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">File:</span> <!></div>'),nr=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Instruction:</span> <span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),rr=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),ir=C('<div class="c-history-header svelte-8btr94"><div class="c-history-header__timestamp svelte-8btr94"> </div> <div class="c-history-header__metadata svelte-8btr94"><div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Request ID:</span> <!></div> <!> <!> <!></div></div>');function rn(n,t){zt(t,!1);let e=H(t,"occuredAt",8),r=H(t,"requestID",8),a=H(t,"pathName",8,""),s=H(t,"repoRoot",8),y=H(t,"prompt",8,""),d=H(t,"others",24,()=>[]);function o(){Ot.postMessage({type:ft.openFile,data:{repoRoot:s(),pathName:a()}})}Wt();var c=ir(),u=h(c),g=h(u),x=S(u,2),b=h(x),R=S(h(b),2);gn(R,{get text(){return r()},variant:"ghost-block",color:"neutral",size:1,tooltip:"Copy Request ID",$$slots:{text:(m,O)=>{var z=Kn(),E=h(z);dt(()=>mt(E,r())),_(m,z)}}});var N=S(b,2),T=m=>{var O=er(),z=S(h(O),2);Me(z,{variant:"ghost-block",color:"neutral",size:1,title:"Click to open file",$$events:{click:o},children:(E,J)=>{var tt=tr(),ot=h(tt);dt(()=>mt(ot,`‎${a()??""}`)),_(E,tt)},$$slots:{default:!0}}),_(m,O)};X(N,m=>{a()&&m(T)});var l=S(N,2),f=m=>{var O=nr(),z=S(h(O),2),E=h(z);dt(()=>mt(E,y())),_(m,O)};X(l,m=>{y()&&m(f)});var k=S(l,2);At(k,1,d,re,(m,O)=>{var z=rr(),E=h(z),J=h(E);dt(()=>mt(J,i(O))),_(m,z)}),dt(m=>mt(g,m),[()=>(P(Xe),P(e()),w(()=>Xe(e(),"p 'on' P")))],U),_(n,c),Rt()}const _e={lineNumbers:"off",padding:{top:18,bottom:18}};var ar=C('<div class="c-completion-code-block svelte-krgqjl"><div class="c-completion-code-block__content svelte-krgqjl"><!></div></div>');const Je=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"],ne=new class{constructor(){Re(this,"_state");this._state=Ot.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(n){return this._state.feedback[n]?this._state.feedback[n]:{selectedRating:at.unset,feedbackNote:""}}setFeedback(n,t){this._state.feedback[n]=t,Ot.setState(this._state)}cleanupFeedback(n){for(const t of Object.keys(this._state.feedback))n[t]||delete this._state.feedback[t];Ot.setState(this._state)}};function le(n){return typeof n=="string"?n:n==null?void 0:n.value}var or=C('<div><div class="background-slider svelte-axvozx"></div> <!></div>');function an(n,t){zt(t,!1);let e=H(t,"options",8),r=H(t,"size",8,2),a=H(t,"disabled",8,!1),s=H(t,"onSelectOption",8),y=H(t,"activeOption",28,()=>le(e()[0])),d=D(),o=D();function c(){var k;const l=(k=i(d))==null?void 0:k.querySelectorAll(".c-toggle-button__button");if(!l)return;const f=l[e().findIndex(m=>le(m)===y())];if(i(d)&&i(o)&&f){const m=f.getBoundingClientRect(),O=i(d).getBoundingClientRect();ht(o,i(o).style.left=m.left-O.left+"px"),ht(o,i(o).style.width=`${m.width}px`),ht(o,i(o).style.height=`${m.height}px`)}}let u=D(),g=D(!1),x=D();Ze(()=>{var l;(l=i(u))==null||l.disconnect(),M(u,void 0),clearTimeout(i(x))}),L(()=>P(y()),()=>{y()&&c()}),L(()=>(i(d),i(u),i(x)),()=>{i(d)&&!i(u)&&(M(u,new ResizeObserver(()=>{M(g,!0),c(),clearTimeout(i(x)),M(x,setTimeout(()=>{M(g,!1)},100))})),i(u).observe(i(d)))}),de(),Wt();var b=or();let R;var N=h(b);Oe(N,l=>M(o,l),()=>i(o));var T=S(N,2);At(T,1,e,re,(l,f)=>{const k=U(()=>i(f)===y()?"c-toggle-button__button--active":"");xn(l,{get size(){return r()},get disabled(){return a()},variant:"ghost",color:"neutral",get class(){return`c-toggle-button__button ${i(k)??""}`},$$events:{click:()=>function(m){!a()&&s()(m)&&y(m)}(le(i(f)))},children:(m,O)=>{var z=Ve(),E=Dt(z);const J=U(()=>(i(f),w(()=>le(i(f)))));mn(E,t,"option-button-contents",{get option(){return i(J)},get size(){return r()}},tt=>{const ot=U(()=>r()===.5?1:r());hn(tt,{get size(){return i(ot)},children:(gt,wt)=>{var Ft=ke();dt(()=>mt(Ft,(i(f),w(()=>typeof i(f)=="string"?i(f):i(f).label)))),_(gt,Ft)},$$slots:{default:!0}})}),_(m,z)},$$slots:{default:!0}})}),Oe(b,l=>M(d,l),()=>i(d)),dt(l=>R=Ne(b,1,"c-toggle-button svelte-axvozx",null,R,l),[()=>({"c-toggle-button--disabled":a(),"c-toggle-button--size-0_5":r()===.5,"c-toggle-button--size-1":r()===1,"c-toggle-button--size-2":r()===2,"c-toggle-button--size-3":r()===3,"c-toggle-button--size-4":r()===4,"c-toggle-button--resizing":i(g)})],U),_(n,b),Rt()}var sr=C('<div class="c-unified-history-item__tabs svelte-179mxe5"><!></div>'),cr=C('<div class="c-unified-history-item__code-block svelte-179mxe5"><!></div>'),ur=C('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext"><code><span> </span></code></pre></div> <div> </div> <section>original: <!> modified: <!></section>',1),lr=C('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext" class="c-next-edit-addition svelte-179mxe5"><code><span> </span></code></pre></div>'),dr=C('<section><!> <div class="c-unified-history-item__no-modifications svelte-179mxe5">Unchanged locations:</div> <!></section>'),mr=C('<div class="c-unified-history-item__feedback-area svelte-179mxe5"><div class="c-unified-history-item__feedback-content svelte-179mxe5"><!></div> <div class="c-unified-history-item__feedback-actions svelte-179mxe5"><!> <!></div></div>'),hr=C('<div class="c-unified-history-item__header svelte-179mxe5"><!></div> <!> <div class="c-unified-history-item__content svelte-179mxe5"><!></div> <div class="c-unified-history-item__footer svelte-179mxe5"><div class="c-unified-history-item__ratings svelte-179mxe5"><div class="c-unified-history-item__rating-buttons svelte-179mxe5"><!> <!></div> <div class="c-unified-history-item__thankyou svelte-179mxe5"> </div></div> <!></div>',1);function xe(n,t){zt(t,!1);const e=D(),r=D(),a=D(),s=D(),y=D(),d=D();let o=H(t,"completion",24,()=>{}),c=H(t,"nextEdit",24,()=>{}),u=H(t,"demo",8,!1),g=D(i(e)==="completion"?"Completion":"Next Edit");const x=i(e)==="completion"?["Completion"]:["Next Edit"];let b,R,N=D(ne.getFeedback(i(r))),T=D(!1),l=D(""),f=D(!1),k=null,m=D(""),O=D(),z=D([]),E=D([]);function J(){M(l,Je[Math.floor(Math.random()*Je.length)]),b&&clearTimeout(b),b=setTimeout(()=>{M(l,"")},4e3)}function tt(p){ht(N,i(N).selectedRating=p),k=p,M(m,""),M(f,!0)}function ot(){M(f,!1),k=null,M(m,""),ht(N,i(N).selectedRating=at.unset)}function gt(){k&&i(m).trim().length!==0&&(function(p,W){if(J(),R=i(N).selectedRating,p!==at.unset&&ht(N,i(N).selectedRating=p),u())return;let j=W||i(N).feedbackNote;ne.setFeedback(i(r),i(N)),M(T,!0);const Q=i(e)==="completion"?ft.completionRating:ft.nextEditRating;Ot.postMessage({type:Q,data:{requestId:i(r),rating:p,note:j.trim()}})}(k,i(m).trim()),ot())}function wt(p){Ot.postMessage({type:ft.openFile,data:{repoRoot:p.qualifiedPathName.rootPath,pathName:p.result.path,range:p.lineRange,differentTab:!0}}),M(O,p)}function Ft(p){return M(g,p),!0}L(()=>P(o()),()=>{M(e,o()?"completion":"nextEdit")}),L(()=>(P(o()),P(c())),()=>{var p,W;M(r,((p=o())==null?void 0:p.requestId)||((W=c())==null?void 0:W.requestId)||"")}),L(()=>(P(o()),P(c())),()=>{var p,W;M(a,((p=o())==null?void 0:p.occuredAt)||((W=c())==null?void 0:W.occurredAt)||new Date)}),L(()=>P(o()),()=>{var p;M(s,((p=o())==null?void 0:p.pathName)||"")}),L(()=>(P(o()),P(c())),()=>{var p,W,j;M(y,((p=o())==null?void 0:p.repoRoot)||((j=(W=c())==null?void 0:W.qualifiedPathName)==null?void 0:j.rootPath)||"")}),L(()=>i(e),()=>{M(d,"Leave feedback about this "+(i(e)==="completion"?"completion":"next edit"))}),L(()=>(P(c()),ve),()=>{c()&&(M(z,c().suggestions.filter(p=>p.changeType!==ve.noop)),M(E,c().suggestions.filter(p=>p.changeType===ve.noop)))}),de(),Wt(),Pt("message",Ke,function(p){if(u())return;const W=p.data;switch(W.type){case ft.completionRatingDone:{const{requestId:j}=W.data;if(j!==i(r))return;M(T,!1),W.data.success||(ht(N,i(N).selectedRating=R),ne.setFeedback(i(r),i(N)));break}case ft.nextEditRatingDone:{const{requestId:j}=W.data;if(j!==i(r))return;M(T,!1),W.data.success||(ht(N,i(N).selectedRating=R),ne.setFeedback(i(r),i(N)));break}}});const ge=U(()=>"c-unified-history-item "+(i(T)?"c-unified-history-item--sending-feedback":""));yn(n,{size:2,variant:"surface",get class(){return i(ge)},children:(p,W)=>{var j=hr(),Q=Dt(j),yt=h(Q);const Ct=U(()=>(P(c()),w(()=>c()?[`Request type: ${c().mode}/${c().scope}`]:void 0)));rn(yt,{get occuredAt(){return i(a)},get requestID(){return i(r)},get pathName(){return i(s)},get repoRoot(){return i(y)},get others(){return i(Ct)}});var Nt=S(Q,2),jt=F=>{var et=sr();an(h(et),{get options(){return x},onSelectOption:Ft,get activeOption(){return i(g)},size:1}),_(F,et)};X(Nt,F=>{w(()=>x.length>1)&&F(jt)});var Yt=S(Nt,2),Bt=h(Yt),Lt=F=>{var et=Ve(),Y=Dt(et);At(Y,1,()=>(P(o()),w(()=>o().completions)),re,(Z,nt)=>{var vt=cr();(function(st,B){zt(B,!1);let v=H(B,"prefix",8),V=H(B,"suffix",8),I=H(B,"completion",8);const K=function(_t){const lt=_t.split(`
`).slice(-6);for(let xt=0;xt<lt.length;xt++)if(lt[xt].trim().length>0)return lt.slice(xt).join(`
`);return""}(v()),rt=(ct=V(),!!(bt=I().skippedSuffix)&&ct.indexOf(bt)===0);var ct,bt;const It=rt?function(_t,lt){return lt?_t.indexOf(lt)!==0?_t:_t.slice(lt.length):_t}(V(),I().skippedSuffix):V(),St=function(_t){const lt=_t.split(`
`).slice(0,6);for(let xt=lt.length-1;xt>=0;xt--)if(lt[xt].trim().length>0)return lt.slice(0,xt+1).join(`
`);return""}(It),it=I().text,qt=rt?I().skippedSuffix:"",Xt=I().suffixReplacementText,Ut=K+it+qt+Xt+St,G=bn.createModel(Ut,"plaintext"),Ht=G.getPositionAt(0),Jt=G.getPositionAt(K.length),Te=G.getPositionAt(K.length),Pe=G.getPositionAt(K.length+it.length),De=G.getPositionAt(K.length+it.length),Ce=G.getPositionAt(K.length+it.length+qt.length),Se=G.getPositionAt(K.length+it.length+qt.length),qe=G.getPositionAt(K.length+it.length+qt.length+Xt.length),$e=G.getPositionAt(K.length+it.length+qt.length+Xt.length),ze=G.getPositionAt(Ut.length),on=[{range:new Zt(Ht.lineNumber,Ht.column,Jt.lineNumber,Jt.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Zt($e.lineNumber,$e.column,ze.lineNumber,ze.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Zt(Te.lineNumber,Te.column,Pe.lineNumber,Pe.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Zt(Se.lineNumber,Se.column,qe.lineNumber,qe.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Zt(De.lineNumber,De.column,Ce.lineNumber,Ce.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];Ze(()=>{G==null||G.dispose()}),Wt();var We=ar(),sn=h(We),cn=h(sn);pn(cn,{get options(){return _e},get model(){return G},get decorations(){return on}}),_(st,We),Rt()})(h(vt),{get completion(){return i(nt)},get prefix(){return P(o()),w(()=>o().prefix)},get suffix(){return P(o()),w(()=>o().suffix)}}),_(Z,vt)}),_(F,et)},Qt=(F,et)=>{var Y=Z=>{var nt=dr(),vt=h(nt);At(vt,1,()=>i(z),re,(B,v)=>{var V=ur(),I=Dt(V),K=Ae(()=>Ye("Enter",()=>wt(i(v)))),rt=h(I),ct=h(rt),bt=h(ct);let It;var St=h(bt),it=S(I,2),qt=h(it),Xt=S(it,2),Ut=S(h(Xt));me(Ut,{get text(){return i(v),w(()=>i(v).result.existingCode)},get pathName(){return i(v),w(()=>i(v).qualifiedPathName.relPath)},options:{lineNumbers:"off"}});var G=S(Ut,2);me(G,{get text(){return i(v),w(()=>i(v).result.suggestedCode)},get pathName(){return i(v),w(()=>i(v).qualifiedPathName.relPath)},options:{lineNumbers:"off"}}),dt(Ht=>{It=Ne(bt,1,"c-next-edit-addition svelte-179mxe5",null,It,Ht),mt(St,`${i(v),w(()=>i(v).qualifiedPathName.relPath)??""}: ${i(v),w(()=>i(v).lineRange.start+(i(v).lineRange.start<i(v).lineRange.stop?1:0))??""}-${i(v),w(()=>i(v).lineRange.stop)??""}`),mt(qt,(i(v),w(()=>i(v).result.changeDescription)))},[()=>({"c-next-edit-addition-clicked":i(O)===i(v)})],U),Pt("click",I,()=>wt(i(v))),Pt("keydown",I,function(...Ht){var Jt;(Jt=i(K))==null||Jt.apply(this,Ht)}),_(B,V)});var st=S(vt,4);At(st,1,()=>i(E),re,(B,v)=>{var V=lr(),I=Ae(()=>Ye("Enter",()=>wt(i(v)))),K=h(V),rt=h(K),ct=h(rt);let bt;var It=h(ct);dt(St=>{bt=Ne(ct,1,"c-next-edit-addition svelte-179mxe5",null,bt,St),mt(It,`${i(v),w(()=>i(v).qualifiedPathName.relPath)??""}: ${i(v),w(()=>i(v).lineRange.start+(i(v).lineRange.start<i(v).lineRange.stop?1:0))??""}-${i(v),w(()=>i(v).lineRange.stop)??""}`)},[()=>({"c-next-edit-addition-clicked":i(O)===i(v)})],U),Pt("click",V,()=>wt(i(v))),Pt("keydown",V,function(...St){var it;(it=i(I))==null||it.apply(this,St)}),_(B,V)}),_(Z,nt)};X(F,Z=>{i(e)==="nextEdit"&&c()&&Z(Y)},et)};X(Bt,F=>{i(e)==="completion"&&o()?F(Lt):F(Qt,!1)});var A=S(Yt,2),ut=h(A),Mt=h(ut),pt=h(Mt);const $=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.positive?"success":"neutral")));je(pt,{variant:"ghost",get color(){return i($)},size:2,get disabled(){return i(T)},get title(){return i(d)},$$events:{click:()=>tt(at.positive)},children:(F,et)=>{const Y=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.positive)));Ie(F,{iconName:"thumb_up",get fill(){return i(Y)}})},$$slots:{default:!0}});var Gt=S(pt,2);const ae=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.negative?"error":"neutral")));je(Gt,{variant:"ghost",get color(){return i(ae)},size:2,get disabled(){return i(T)},get title(){return i(d)},$$events:{click:()=>tt(at.negative)},children:(F,et)=>{const Y=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.negative)));Ie(F,{iconName:"thumb_down",get fill(){return i(Y)}})},$$slots:{default:!0}});var oe=S(Mt,2),se=h(oe),ce=S(ut,2),pe=F=>{var et=mr(),Y=h(et),Z=h(Y);wn(Z,{rows:"4",placeholder:"Enter your feedback...",resize:"none",get value(){return i(m)},set value(v){M(m,v)},$$legacy:!0});var nt=S(Y,2),vt=h(nt);Me(vt,{variant:"ghost",size:2,$$events:{click:ot},children:(v,V)=>{var I=ke("Cancel");_(v,I)},$$slots:{default:!0}});var st=S(vt,2);const B=U(()=>(i(m),w(()=>i(m).trim().length===0)));Me(st,{variant:"solid",size:2,get disabled(){return i(B)},$$events:{click:gt},children:(v,V)=>{var I=ke("Share Feedback");_(v,I)},$$slots:{default:!0}}),_(F,et)};X(ce,F=>{i(f)&&F(pe)}),dt(()=>mt(se,i(l))),_(p,j)},$$slots:{default:!0}}),Rt()}var fr=C(`<div class="l-no-items svelte-10bvc8"><div class="l-no-items__msg svelte-10bvc8"><h2>History.</h2> <p>As you use Augment, we'll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we'll display for each suggestion.</p></div> <div class="l-no-items__divider svelte-10bvc8"></div> <div class="l-no-items__example svelte-10bvc8"><!></div></div>`),gr=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),pr=C("modified: <!>",1),vr=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),br=C("<section>original: <!> <!></section>"),wr=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn" role="button" tabindex="0">Click to view diff</div>'),yr=C('<div class="c-instruction-item svelte-15p7ohn"><!> <!></div>'),xr=C('<div class="l-items-list__empty svelte-5e6wj2"><!></div>'),kr=C('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Nr=C('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Mr=C('<div class="l-items-list__instructions-section svelte-5e6wj2"><h3 class="l-items-list__section-title svelte-5e6wj2">Instructions</h3> <div class="l-items-list__content svelte-5e6wj2"></div></div>'),_r=C('<div class="l-items-list__empty-panel svelte-5e6wj2"><p> </p></div>'),Tr=C('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Pr=C('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Dr=C('<div class="l-items-list__content svelte-5e6wj2"></div>'),Cr=C('<!> <!> <div class="l-items-list__panel-content"><!></div>',1),Sr=C('<main class="l-items-list svelte-5e6wj2"><!></main>');dn(function(n,t){zt(t,!1);const e=D(),r=D(),a=D(),s=D(),y=D();let d=D({}),o=D({}),c=D({});function u(T){for(const l of T)i(d)[l.requestId]||ht(d,i(d)[l.requestId]={...l,occuredAt:we(l.occuredAt)});ne.cleanupFeedback(i(d))}function g(T){for(const l of T)if(!i(o)[l.requestId]){if(typeof l.occuredAt=="string"){const f=l.occuredAt;l.occuredAt=we(f)}ht(o,i(o)[l.requestId]={...l})}}function x(T){for(const l of T)l.suggestions.length!==0&&ht(c,i(c)[l.requestId]={requestId:l.requestId,occuredAt:we(l.occurredAt),result:l})}Ot.postMessage({type:ft.historyLoaded});let b=D([]),R=D("Completions");function N(T){return M(R,T),!0}L(()=>(i(o),i(d),i(c)),()=>{M(b,[...Object.values(i(o)),...Object.values(i(d)),...Object.values(i(c))].sort((T,l)=>l.occuredAt.getTime()-T.occuredAt.getTime()))}),L(()=>i(b),()=>{M(e,i(b).filter(T=>"completions"in T))}),L(()=>i(b),()=>{M(r,i(b).filter(T=>"result"in T))}),L(()=>i(b),()=>{M(a,i(b).filter(T=>"prompt"in T))}),L(()=>(i(e),i(r)),()=>{M(s,[{value:"Completions",label:`Completions ${i(e).length}`},{value:"Next Edits",label:`Next Edits ${i(r).length}`}])}),L(()=>(i(R),i(e),i(r)),()=>{M(y,i(R)==="Completions"?i(e):i(r))}),de(),Wt(),Pt("message",Ke,function(T){const l=T.data;switch(l.type){case ft.historyInitialize:g(l.data.instructions),u(l.data.completionRequests),x(l.data.nextEdits);break;case ft.completions:u(l.data);break;case ft.instructions:g(l.data);break;case ft.nextEditSuggestions:x([l.data])}}),vn.Root(n,{children:(T,l)=>{var f=Sr(),k=h(f),m=z=>{var E=xr();(function(J,tt){zt(tt,!1);const ot={occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`};Wt();var gt=fr(),wt=S(h(gt),4);xe(h(wt),{get completion(){return ot},demo:!0}),_(J,gt),Rt()})(h(E),{}),_(z,E)},O=z=>{var E=Cr(),J=Dt(E);an(J,{get options(){return i(s)},onSelectOption:N,get activeOption(){return i(R)},size:2});var tt=S(J,2),ot=p=>{var W=Mr(),j=S(h(W),2);At(j,7,()=>i(a),Q=>Q.requestId,(Q,yt,Ct)=>{var Nt=Nr(),jt=Dt(Nt),Yt=h(jt);const Bt=U(()=>(i(yt),w(()=>function(A){if(!("prompt"in A))throw new Error("wrong type");if("completions"in A)throw new Error("wrong type");return A}(i(yt)))));(function(A,ut){zt(ut,!1);const Mt=D(),pt=D();let $=H(ut,"instruction",8),Gt=D(void 0);function ae(){M(Gt,$().requestId)}function oe(Y){const Z=Y.split(`
`);for(let nt=Z.length-1;nt>=0;nt--)if(Z[nt].trim().length>0)return Z.slice(0,nt+1).join(`
`);return""}L(()=>P($()),()=>{M(Mt,oe($().selectedText))}),L(()=>P($()),()=>{M(pt,oe($().modifiedText))}),de(),Wt();var se=yr(),ce=h(se);rn(ce,{get occuredAt(){return P($()),w(()=>$().occuredAt)},get requestID(){return P($()),w(()=>$().requestId)},get pathName(){return P($()),w(()=>$().pathName)},get repoRoot(){return P($()),w(()=>$().repoRoot)},get prompt(){return P($()),w(()=>$().prompt)}});var pe=S(ce,2),F=Y=>{var Z=gr();_(Y,Z)},et=(Y,Z)=>{var nt=st=>{var B=br(),v=S(h(B));me(v,{get options(){return _e},get text(){return i(Mt)},get pathName(){return P($()),w(()=>$().pathName)}});var V=S(v,2),I=rt=>{var ct=pr(),bt=S(Dt(ct));me(bt,{get options(){return _e},get text(){return i(pt)},get pathName(){return P($()),w(()=>$().pathName)}}),_(rt,ct)},K=rt=>{var ct=vr();_(rt,ct)};X(V,rt=>{i(Mt)!==i(pt)?rt(I):rt(K,!1)}),_(st,B)},vt=st=>{var B=wr();Pt("keyup",B,ae),Pt("click",B,ae),_(st,B)};X(Y,st=>{P($()),i(Gt),w(()=>$().userRequested||i(Gt)===$().requestId)?st(nt):st(vt,!1)},Z)};X(pe,Y=>{P($()),w(()=>$().selectedText===$().modifiedText)?Y(F):Y(et,!1)}),_(A,se),Rt()})(Yt,{get instruction(){return i(Bt)}});var Lt=S(jt,2),Qt=A=>{var ut=kr();_(A,ut)};X(Lt,A=>{P(i(Ct)),i(a),w(()=>i(Ct)<i(a).length-1)&&A(Qt)}),_(Q,Nt)}),_(p,W)};X(tt,p=>{i(a),w(()=>i(a).length>0)&&p(ot)});var gt=S(tt,2),wt=h(gt),Ft=p=>{var W=_r(),j=h(W),Q=h(j);dt(yt=>mt(Q,`No ${yt??""} found.`),[()=>(i(R),w(()=>i(R).toLowerCase()))],U),_(p,W)},ge=p=>{var W=Dr();At(W,7,()=>i(y),j=>j.requestId,(j,Q,yt)=>{var Ct=Pr(),Nt=Dt(Ct),jt=h(Nt),Yt=A=>{xe(A,{get completion(){return i(Q)}})},Bt=(A,ut)=>{var Mt=pt=>{xe(pt,{get nextEdit(){return i(Q),w(()=>i(Q).result)}})};X(A,pt=>{"result"in i(Q)&&pt(Mt)},ut)};X(jt,A=>{"completions"in i(Q)?A(Yt):A(Bt,!1)});var Lt=S(Nt,2),Qt=A=>{var ut=Tr();_(A,ut)};X(Lt,A=>{P(i(yt)),i(y),w(()=>i(yt)<i(y).length-1)&&A(Qt)}),_(j,Ct)}),_(p,W)};X(wt,p=>{i(y),w(()=>i(y).length===0)?p(Ft):p(ge,!1)}),_(z,E)};X(k,z=>{i(b),w(()=>!i(b).length)?z(m):z(O,!1)}),_(T,f)},$$slots:{default:!0}}),Rt()},{target:document.getElementById("app")});
