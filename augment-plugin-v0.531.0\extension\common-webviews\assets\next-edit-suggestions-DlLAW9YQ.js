const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./next-edit-suggestions-IW1pin9L.css","./NextEditSuggestions-B5gIbQNv.js","./legacy-YP6Kq8lu.js","./SpinnerAugment-Dpcl1cXc.js","./SpinnerAugment-DoxdFmoV.css","./next-edit-types-904A5ehg.js","./host-BNehKqab.js","./IconFilePath-D3BkWpGW.js","./LanguageIcon-D5Xb9jVX.js","./LanguageIcon-D78BqCXT.css","./IconButtonAugment-CbpcmeFk.js","./IconButtonAugment-B4afvB2A.css","./IconFilePath-BVaLv7mP.css","./async-messaging-gS_K9w3p.js","./Drawer-CU8_j807.js","./index-DOexUbEr.js","./ellipsis-CW5cyp36.js","./Drawer-u8LRIFRf.css","./keypress-DD1aQVr0.js","./VSCodeCodicon-DQWBC8l_.js","./VSCodeCodicon-DVaocTud.css","./svelte-component-Uytug4gU.js","./monaco-render-utils-DfwV7QLY.js","./toggleHighContrast-Cb9MCs64.js","./preload-helper-Dv6uf1Os.js","./toggleHighContrast-D4zjdeIP.css","./index-hRm--fCg.js","./index-4vhrZf9p.js","./index-BlHvDt2c.css","./isObjectLike-BNqj-rl6.js","./ButtonAugment-DkEdzEZO.js","./ButtonAugment-DORgvEFm.css","./NextEditSuggestions-Q98kphIR.css"])))=>i.map(i=>d[i]);
import{x as O,m as Z,T as ee,a8 as ae,F as D,M as te,I as ie,b as o,L as F,z as ne,G as _,o as l,K as c,N as k,O as b,P,C as G,u as oe,ap as re}from"./legacy-YP6Kq8lu.js";import{p as r,T as f,h as se,S as le}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";import{_ as q}from"./preload-helper-Dv6uf1Os.js";import{a as ge}from"./await-NDiL5Mzl.js";import{t as A,a as C}from"./index-DOexUbEr.js";import{A as x}from"./augment-logo-D8bZBTPs.js";import{M as de}from"./index-hRm--fCg.js";import"./index-4vhrZf9p.js";const B={messages:["Untangling strings...","Warming up GPUs...","Initializing quantum compiler...","Procuring topological qubits...","Releasing AI pigeons...","Building mechanical keyboards...","Downloading more RAM...","Solving P vs. NP...","Summoning code wizards...","Folding origami...","Caffeinating the algorithms...","Phoning home...","Popping bubble wrap...","Dividing by zero...","Refactoring the matrix...","Petting cat...","Counting to infinity...","Knitting tea cozy...","Planting syntax tree...","Touching grass...","Code whispering...","Simulating quantum foam...","Aligning eigenspaces...","Reticulating splines...","Calculating terminal velocity...","Preparing jump to lightspeed...","Charging hyperdrive coils...","Aligning dilithium crystals...","Negotiating with Jawas...","Searching for droids...","Launching Kamehameha wave...","Modulating shield frequencies...","Fixing hyperdrive, again...","Computing odds of survival...","Getting a snack...","Assembling rubber ducks...","Overflowing stacks...","Waking up agents...","Searching haystacks...","Plugging in guitars...","Winding back the tape...","Onboarding stakeholders...","Thinking outside the box...","Moving the needle...","Dusting the backlog...","Calculating story points...","Putting it all on black...","Betting the farm...","Generating more loading messages...","Consulting Deep Thought...","Stretching hammies...","Grinding for XP...","Loading save point...","Replacing vacuum tubes...","Checking internet weather...","Turning it off and on again...","Searching gitblame..."],errors:["That didn't quite work. Let me try again.","Something went wrong, sorry about that. Trying again.","Hmm this isn't working. Looking for another way.","I seem to have encountered an issue, sorry about that. Let me try again.","That didn't go as planned. Recalibrating...","I need to take a different approach. One moment...","Hmm, something is not right. Let me find a better solution.","Looks like I need to rethink this. Finding alternatives.","Sorry for the delay, let me try again.","I need one more minute, thanks for your patience. Trying again now.","Something didn't work, giving it another try now.","One moment, let me see if I can try again.","I think I got something wrong, thanks for your patience while I take another look.","Give me one second to think this through - I need to try again.","Something doesn't look right, let me give it another shot."]};var me=_('<div class="l-component svelte-1foy1hj"><!></div>'),ce=_('<code class="svelte-1foy1hj"> </code>'),he=_('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container l-loader-error-message svelte-1foy1hj"><!> <!></div></div>'),ue=_('<div class="l-loader svelte-1foy1hj"><div class="l-loader__logo svelte-1foy1hj"><!> <!></div> <div class="l-loader__message-container svelte-1foy1hj"><!> <!></div></div>');re(function(K,N){O(N,!1);const W=async()=>(await q(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),(await q(async()=>{const{default:I}=await import("./NextEditSuggestions-B5gIbQNv.js");return{default:I}},__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32]),import.meta.url)).default);D(),de.Root(K,{children:(I,ve)=>{(function(H,t){O(t,!1);let V=r(t,"minDisplayTime",8,1e3),U=r(t,"loader",8),J=r(t,"props",8),L=r(t,"title",8,"Augment Code"),X=r(t,"randomize",8,!0),S=r(t,"retryCount",8,3),T=r(t,"loadingMessages",24,()=>B.messages),$=r(t,"errorMessages",24,()=>B.errors),z=r(t,"errorMessage",8,"An error occurred while loading. Please try again later."),h=T().slice(1),j=Z(T()[0]),u="loading",M=new AbortController;async function R(i=0){try{const[a]=await Promise.all([U()(),(n=V(),e=M.signal,new Promise(g=>{const v=setTimeout(g,n);e&&e.addEventListener("abort",()=>{clearTimeout(v),g()})}))]);return a}catch(a){if(console.error("Failed to load component",a),u="retry",i===0&&(h=[...$()]),S()&&i<=S())return await R(i+1);throw u="error",new Error("Failed to load component after retrying. Please try again later.")}var n,e}ee(()=>M.abort()),ae(async function(){h.length===0&&(h=[...u==="retry"?$():T()]),ne(j,u==="error"?z():h.splice(u!=="retry"&&X()?Math.floor(Math.random()*h.length):0,1)[0]??"")}),D();var E=te(),Q=ie(E);ge(Q,()=>oe(R),i=>{var n=ue(),e=l(n),a=l(e);x(a);var g=c(a,2);f(g,{size:2,children:(p,d)=>{var s=k();b(()=>P(s,L())),o(p,s)},$$slots:{default:!0}});var v=c(e,2),y=l(v);le(y,{});var w=c(y,2);f(w,{size:1,color:"secondary",children:(p,d)=>{var s=k();b(()=>P(s,G(j))),o(p,s)},$$slots:{default:!0}}),A(3,n,()=>C),o(i,n)},(i,n)=>{var e=me(),a=l(e);G(n)(a,se(J)),A(3,e,()=>C),o(i,e)},(i,n)=>{var e=he(),a=l(e),g=l(a);x(g);var v=c(g,2);f(v,{size:3,children:(d,s)=>{var m=k();b(()=>P(m,L())),o(d,m)},$$slots:{default:!0}});var y=c(a,2),w=l(y);f(w,{size:3,children:(d,s)=>{var m=k("An Error Occurred.");o(d,m)},$$slots:{default:!0}});var p=c(w,2);f(p,{size:1,children:(d,s)=>{var m=ce(),Y=l(m);b(()=>P(Y,z())),o(d,m)},$$slots:{default:!0}}),A(3,e,()=>C),o(i,e)}),o(H,E),F()})(I,{loader:W,props:{}})},$$slots:{default:!0}}),F()},{target:document.getElementById("app")});
