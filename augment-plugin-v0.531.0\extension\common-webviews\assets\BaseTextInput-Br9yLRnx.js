import{x as m,y as x,D as b,F as z,G as y,a as g,C as h,H as B,m as C,M as D,I,b as l,o as j,L as A,z as F,B as G}from"./legacy-YP6Kq8lu.js";import{p as t,T as H,a as L,d as i}from"./SpinnerAugment-Dpcl1cXc.js";import"./IconButtonAugment-CbpcmeFk.js";var M=y("<div><!></div>");function E(u,a){m(a,!1);const o=C();let p=t(a,"variant",8,"surface"),c=t(a,"size",8,2),v=t(a,"type",8,"default"),s=t(a,"color",24,()=>{});x(()=>(G(s()),i),()=>{F(o,s()?i(s()):i("accent"))}),b(),z();var e=M();g(e,r=>({...h(o),class:`c-base-text-input c-base-text-input--${p()} c-base-text-input--size-${c()}`,[B]:r}),[()=>({"c-base-text-input--has-color":s()!==void 0})],"svelte-1mx5zy6");var d=j(e);H(d,{get type(){return v()},get size(){return c()},children:(r,T)=>{var n=D(),f=I(n);L(f,a,"default",{},null),l(r,n)},$$slots:{default:!0}}),l(u,e),A()}export{E as B};
