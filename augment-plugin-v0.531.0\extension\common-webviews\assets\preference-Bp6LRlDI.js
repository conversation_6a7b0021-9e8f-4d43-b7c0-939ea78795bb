import{G as h,J as Q,o,K as a,O as E,Q as gt,Y as P,a1 as $,b as p,P as et,_ as ft,x as dt,$ as yt,m,a8 as wt,ak as pt,C as t,z as c,y as U,B as tt,D as kt,F as mt,u as $t,L as ht,I as bt,a9 as At,M as qt,ap as Bt}from"./legacy-YP6Kq8lu.js";import{p as b}from"./SpinnerAugment-Dpcl1cXc.js";import{h as B,W as J}from"./host-BNehKqab.js";import{aJ as lt}from"./AugmentMessage-DEVbs_ts.js";import{b as xt,a as Mt}from"./input-C2nR_fsN.js";import{C as Ct,S as Rt}from"./folder-opened-Dq2m1kHY.js";import{M as It}from"./message-broker-DRrss2z_.js";import{s as St}from"./chat-model-context-DZ2DTs5O.js";import{M as Dt}from"./index-hRm--fCg.js";import"./index-DOexUbEr.js";import"./CalloutAugment-0Y9u1WCc.js";import"./CardAugment-D6yVifBE.js";import"./IconButtonAugment-CbpcmeFk.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-vTFXV1kt.js";import"./async-messaging-gS_K9w3p.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-BNqj-rl6.js";import"./BaseTextInput-Br9yLRnx.js";import"./lodash-DfmeyYaq.js";import"./svelte-component-Uytug4gU.js";import"./Filespan-Dfz0pJHr.js";import"./index-4vhrZf9p.js";import"./diff-operations-ClfBxOIy.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await-NDiL5Mzl.js";import"./OpenFileButton-fKwL5bu0.js";import"./index-B528snJk.js";import"./remote-agents-client-DbhVjGoZ.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-BNum2ZUy.js";import"./TextAreaAugment-Bs79KMH3.js";import"./ButtonAugment-DkEdzEZO.js";import"./CollapseButtonAugment-DGKMbvAN.js";import"./partner-mcp-utils-Bk5-h15i.js";import"./tool-types-Chbmg_E2.js";import"./MaterialIcon-j5PxZ6X_.js";import"./CopyButton-CuvLtN0u.js";import"./copy-ChvqXPeP.js";import"./ellipsis-CW5cyp36.js";import"./LanguageIcon-D5Xb9jVX.js";import"./augment-logo-D8bZBTPs.js";import"./file-type-utils-D6OEcQY2.js";var Pt=h('<div class="header svelte-1894wv4"> </div>'),Wt=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(j,x){let i=b(x,"selected",12,null),M=b(x,"question",8,null);function v(w){i(w)}var e=Wt(),d=o(e),W=w=>{var O=Pt(),K=o(O);E(()=>et(K,M())),p(w,O)};Q(d,w=>{M()&&w(W)});var C=a(d,2),g=o(C);let f;var r=a(g,2);let A;var y=a(r,2);let s;var R=a(y,2);let X;var _=a(R,2);let Y;var z=a(_,2);let G;var H=a(z,2);let Z;E((w,O,K,at,st,l,u)=>{f=P(g,1,"button large svelte-1894wv4",null,f,w),A=P(r,1,"button medium svelte-1894wv4",null,A,O),s=P(y,1,"button small svelte-1894wv4",null,s,K),X=P(R,1,"button equal svelte-1894wv4",null,X,at),Y=P(_,1,"button small svelte-1894wv4",null,Y,st),G=P(z,1,"button medium svelte-1894wv4",null,G,l),Z=P(H,1,"button large svelte-1894wv4",null,Z,u)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})],gt),$("click",g,()=>v("A3")),$("click",r,()=>v("A2")),$("click",y,()=>v("A1")),$("click",R,()=>v("=")),$("click",_,()=>v("B1")),$("click",z,()=>v("B2")),$("click",H,()=>v("B3")),p(j,e)}var _t=h('<div class="question svelte-1i0f73l"> </div>'),zt=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Ot=h('<button class="button svelte-2k5n"> </button>'),Ft=h("<div> </div>"),Lt=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Et=h("<!> <!> <!> <!> <!> <!>",1),Jt=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Qt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function jt(j,x){dt(x,!1);const i=m(),M=m(),v=m();let e=b(x,"inputData",8);const d=yt();let W=new Ct(new It(B),B,new Rt);St(W);let C=m(null),g=m(null),f=null,r=m(null),A=m(""),y=m(!1),s=m({a:null,b:null}),R=m(e().data.a.response.length>0&&e().data.b.response.length>0);function X(){if(f="=",t(r)===null)return void d("notify","Overall rating is required");const l={overallRating:t(r),formattingRating:t(C)||"=",hallucinationRating:f||"=",instructionFollowingRating:t(g)||"=",isHighQuality:t(y),textFeedback:t(A)};d("result",l)}wt(()=>{window.addEventListener("message",l=>{const u=l.data;u.type===J.chatModelReply?(u.stream==="A"?pt(s,t(s).a=u.data.text):u.stream==="B"&&pt(s,t(s).b=u.data.text),c(s,t(s))):u.type===J.chatStreamDone&&c(R,!0)})}),U(()=>t(r),()=>{var l;c(i,(l=t(r))==="="||l===null?"Is this a high quality comparison?":`Are you completely happy with response '${l.startsWith("A")?"A":"B"}'?`)}),U(()=>(t(s),tt(e())),()=>{c(M,t(s).a!==null?t(s).a:e().data.a.response)}),U(()=>(t(s),tt(e())),()=>{c(v,t(s).b!==null?t(s).b:e().data.b.response)}),U(()=>tt(e()),()=>{c(R,e().data.a.response.length>0&&e().data.b.response.length>0)}),kt(),mt();var _=Qt(),Y=o(_),z=a(o(Y),2);lt(z,{get markdown(){return tt(e()),$t(()=>e().data.a.message)}});var G=a(z,4),H=o(G),Z=a(o(H),2);lt(Z,{get markdown(){return t(M)}});var w=a(H,4),O=a(o(w),2);lt(O,{get markdown(){return t(v)}});var K=a(G,4),at=l=>{var u=Et(),ot=bt(u);nt(ot,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return t(C)},set selected(n){c(C,n)},$$legacy:!0});var rt=a(ot,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return t(g)},set selected(n){c(g,n)},$$legacy:!0});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return t(r)},set selected(n){c(r,n)},$$legacy:!0});var vt=a(ut,2);(function(n,k){let N=b(k,"isChecked",12,!1),I=b(k,"question",8,null);var q=Lt(),S=o(q),F=D=>{var V=Ft(),it=o(V);E(()=>et(it,I())),p(D,V)};Q(S,D=>{I()&&D(F)});var L=a(S,2),T=o(L);Mt(T,N),p(n,q)})(vt,{get question(){return t(i)},get isChecked(){return t(y)},set isChecked(n){c(y,n)},$$legacy:!0});var ct=a(vt,2);(function(n,k){let N=b(k,"value",12,""),I=b(k,"question",8,null),q=b(k,"placeholder",8,"");var S=zt(),F=o(S),L=D=>{var V=_t(),it=o(V);E(()=>et(it,I())),p(D,V)};Q(F,D=>{I()&&D(L)});var T=a(F,2);E(()=>ft(T,"placeholder",q())),xt(T,N),p(n,S)})(ct,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return t(A)},set value(n){c(A,n)},$$legacy:!0}),function(n,k){let N=b(k,"label",8,"Submit"),I=b(k,"onClick",8);var q=Ot(),S=o(q);E(()=>et(S,N())),$("click",q,function(...F){var L;(L=I())==null||L.apply(this,F)}),p(n,q)}(a(ct,2),{label:"Submit",onClick:X}),p(l,u)},st=l=>{var u=Jt();p(l,u)};Q(K,l=>{t(R)?l(at):l(st,!1)}),p(j,_),ht()}var Gt=h("<main><!></main>");function Ht(j,x){dt(x,!1);let i=m();function M(e){const d=e.detail;B.postMessage({type:J.preferenceResultMessage,data:d})}function v(e){B.postMessage({type:J.preferenceNotify,data:e.detail})}B.postMessage({type:J.preferencePanelLoaded}),mt(),$("message",At,function(e){const d=e.data;d.type===J.preferenceInit&&c(i,d.data)}),Dt.Root(j,{children:(e,d)=>{var W=Gt(),C=o(W),g=f=>{var r=qt(),A=bt(r),y=s=>{jt(s,{get inputData(){return t(i)},$$events:{result:M,notify:v}})};Q(A,s=>{t(i).type==="Chat"&&s(y)}),p(f,r)};Q(C,f=>{t(i)&&f(g)}),p(e,W)},$$slots:{default:!0}}),ht()}(async function(){B&&B.initialize&&await B.initialize(),Bt(Ht,{target:document.getElementById("app")})})();
