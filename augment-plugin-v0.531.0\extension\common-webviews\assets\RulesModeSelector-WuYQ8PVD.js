import{x as ee,y as Q,z as v,m as f,B as k,C as e,D as ae,F as se,M as te,I as y,J as F,u as h,b as a,L as le,N as A,G as E,O as U,P as _,o as oe,K as q,Q as re}from"./legacy-YP6Kq8lu.js";import{e as ie,i as ne}from"./host-BNehKqab.js";import{p as G,b as de,e as ce,f as ue}from"./SpinnerAugment-Dpcl1cXc.js";import{A as ve,D as d}from"./index-vTFXV1kt.js";import{B as H}from"./ButtonAugment-DkEdzEZO.js";import{C as fe}from"./chevron-down-B0l__RXq.js";import{T as he}from"./CardAugment-D6yVifBE.js";import{R}from"./chat-types-BfwvR7Kn.js";var pe=E('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=E("<!> <!>",1),ge=E("<!> <!>",1),$e=E("<!> <!>",1);function xe(J,T){ee(T,!1);const[B,K]=de(),p=()=>ce(e(C),"$focusedIndex",B),w=f(),S=f(),s=f();let O=G(T,"onSave",8),i=G(T,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=f(void 0),I=f(()=>{});Q(()=>k(i()),()=>{v(w,i().path)}),Q(()=>k(i()),()=>{v(S,i().type)}),Q(()=>e(S),()=>{v(s,x.find(t=>t.value===e(S)))}),ae(),se();var W=te(),P=y(W),j=t=>{he(t,{content:"Workspace guidelines are always applied",children:(r,X)=>{H(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var L=A("Always");a(m,L)},$$slots:{default:!0}})},$$slots:{default:!0}})},V=t=>{d.Root(t,{get requestClose(){return e(I)},set requestClose(r){v(I,r)},get focusedIndex(){return e(C)},set focusedIndex(r){ue(v(C,r),"$focusedIndex",B)},children:(r,X)=>{var m=$e(),D=y(m);d.Trigger(D,{children:(M,Z)=>{var c=pe(),g=oe(c);H(g,{color:"neutral",size:1,variant:"soft",children:(u,N)=>{var l=A();U(()=>_(l,(e(s),h(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,N)=>{fe(u,{slot:"iconRight"})}}}),a(M,c)},$$slots:{default:!0}});var L=q(D,2);d.Content(L,{side:"bottom",align:"start",children:(M,Z)=>{var c=ge(),g=y(c);ie(g,1,()=>x,ne,(l,o)=>{const $=re(()=>(e(s),e(o),h(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(I)();try{await O()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e($)},children:(n,b)=>{var z=A();U(()=>_(z,(e(o),h(()=>e(o).label)))),a(n,z)},$$slots:{default:!0}})});var u=q(g,2),N=l=>{var o=me(),$=y(o);d.Separator($,{});var n=q($,2);d.Label(n,{children:(b,z)=>{var Y=A();U(()=>_(Y,(p(),e(s),h(()=>p()!==void 0?x[p()].description:e(s).description)))),a(b,Y)},$$slots:{default:!0}}),a(l,o)};F(u,l=>{(p()!==void 0||e(s))&&l(N)}),a(M,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};F(P,t=>{e(w),h(()=>e(w)===ve)?t(j):t(V,!1)}),a(J,W),le(),K()}export{xe as R};
