import{x as w,y as l,D as L,G as d,O as k,Y as u,Z as x,C as r,m as n,_ as y,P as D,o as G,b as A,L as z,z as t,B as m}from"./legacy-YP6Kq8lu.js";import{p as e}from"./SpinnerAugment-Dpcl1cXc.js";var B=d("<span> </span>");function I(h,a){w(a,!1);let b=e(a,"class",8,""),g=e(a,"iconName",8,""),o=e(a,"fill",8,!1),c=e(a,"grade",8,"normal"),v=e(a,"title",24,()=>{}),p=n(),f=n(),i=n();l(()=>m(o()),()=>{t(p,o()?"1":"0")}),l(()=>m(o()),()=>{t(f,o()?"700":"400")}),l(()=>m(c()),()=>{switch(c()){case"low":t(i,"-25");break;case"normal":t(i,"0");break;case"high":t(i,"200")}}),L();var s=B(),$=G(s);k(()=>{u(s,1,`material-symbols-outlined ${b()}`,"svelte-htlsjs"),x(s,`font-variation-settings: 'FILL' ${r(p)??""}, 'wght' ${r(f)??""}, 'GRAD' ${r(i)??""};`),y(s,"title",v()),D($,g())}),A(h,s),z()}export{I as M};
