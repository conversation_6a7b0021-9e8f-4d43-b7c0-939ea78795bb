var Ti=Object.defineProperty;var Zi=(pe,n,j)=>n in pe?Ti(pe,n,{enumerable:!0,configurable:!0,writable:!0,value:j}):pe[n]=j;var ni=(pe,n,j)=>Zi(pe,typeof n!="symbol"?n+"":n,j);import{x as it,y as N,z as t,m as c,B as a,C as e,D as Mt,F as st,G as v,J as V,o as l,O as ye,Q as se,Y as Ht,Z as Xt,b as i,L as nt,N as de,P as Se,u as o,a8 as ei,T as Ci,K as q,a0 as $i,a1 as ti,_ as Qe,$ as Vi,w as ri,ak as zt,I as we,M as Ft,S as hi,R as Ut,aq as Hi,f as Li,a9 as Ri,ap as Ii}from"./legacy-YP6Kq8lu.js";import{p as E,i as di,e as xt,b as Wt,k as bi,T as qe,S as oi}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";/* empty css                                */import{W as Ai,e as dt,i as mt,h as qi}from"./host-BNehKqab.js";import{M as Ui}from"./message-broker-DRrss2z_.js";import{R as Bt}from"./ra-diff-ops-model-BNum2ZUy.js";import{C as Bi,a as Wi,T as ki,b as Ji,k as Gi}from"./CollapseButtonAugment-DGKMbvAN.js";import{t as Yi,s as Ki}from"./index-DOexUbEr.js";import{c as ui,p as Qi,M as Xi,g as Vt,a as es,i as ts,b as is,P as ci,O as Ei,D as ss,C as ns,E as as}from"./diff-operations-ClfBxOIy.js";import{a as ls,b as os,g as rs,M as ds}from"./index-hRm--fCg.js";import{a as cs,I as yi}from"./IconButtonAugment-CbpcmeFk.js";import{V as gi}from"./VSCodeCodicon-DQWBC8l_.js";import{d as vs,T as jt,a as Ot,C as fs}from"./CardAugment-D6yVifBE.js";import{B as _t}from"./ButtonAugment-DkEdzEZO.js";import{M as mi}from"./MaterialIcon-j5PxZ6X_.js";import{n as ps,g as Ze,a as ai}from"./focusTrapStack-CaEmYw0i.js";import{i as hs,g as Qt,a as us,b as zi,M as gs}from"./file-type-utils-D6OEcQY2.js";import{F as ms,g as li,p as Fi,d as _s}from"./index-B528snJk.js";import{L as Di}from"./LanguageIcon-D5Xb9jVX.js";import{A as ws}from"./async-messaging-gS_K9w3p.js";import{E as xi}from"./exclamation-triangle-CZAYyoF0.js";import{F as ys}from"./Filespan-Dfz0pJHr.js";import{M as Cs}from"./ModalAugment-DfDeRU3v.js";import"./svelte-component-Uytug4gU.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-4vhrZf9p.js";import"./event-modifiers-Bz4QCcZc.js";import"./chat-types-BfwvR7Kn.js";class vi{constructor(n){ni(this,"_opts",null);ni(this,"_subscribers",new Set);this._asyncMsgSender=n}subscribe(n){return this._subscribers.add(n),n(this),()=>{this._subscribers.delete(n)}}notifySubscribers(){this._subscribers.forEach(n=>n(this))}get opts(){return this._opts}updateOpts(n){this._opts=n,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const n=await this._asyncMsgSender.send({type:Ai.remoteAgentDiffPanelLoaded});this.updateOpts(n.data)}catch(n){console.error("Failed to load diff panel:",n),this.updateOpts(null)}}handleMessageFromExtension(n){const j=n.data;return!(!j||!j.type)&&j.type===Ai.remoteAgentDiffPanelSetOpts&&(this.updateOpts(j.data),!0)}}ni(vi,"key","remoteAgentDiffModel");var $s=v("<span><code><!></code></span>");function bs(pe,n){it(n,!1);const j=c(),Ce=c(),h=c(),f=c();let O=E(n,"token",8),le=E(n,"element",28,()=>{});N(()=>a(O()),()=>{t(j,O().raw.slice(1,O().raw.length-1))}),N(()=>e(j),()=>{t(Ce,e(j).startsWith('"'))}),N(()=>e(j),()=>{t(h,/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(e(j)))}),N(()=>(e(h),e(j)),()=>{t(f,e(h)&&function(y){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(y))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let S,I,ne;return y.length===4?(S=parseInt(y.charAt(1),16),I=parseInt(y.charAt(2),16),ne=parseInt(y.charAt(3),16),S*=17,I*=17,ne*=17):(S=parseInt(y.slice(1,3),16),I=parseInt(y.slice(3,5),16),ne=parseInt(y.slice(5,7),16)),.299*S+.587*I+.114*ne<130}(e(j)))}),Mt(),st();var H=$s(),ce=l(H);let M;var K=l(ce),D=y=>{var S=de();ye(()=>Se(S,e(j))),i(y,S)},k=y=>{var S=de();ye(()=>Se(S,e(j))),i(y,S)};V(K,y=>{e(h)?y(D):y(k,!1)}),di(H,y=>le(y),()=>le()),ye(y=>{M=Ht(ce,1,"markdown-codespan svelte-11ta4gi",null,M,y),Xt(ce,e(h)?`background-color: ${e(j)}; color: ${e(f)?"white":"black"}`:"")},[()=>({"markdown-string":e(Ce)})],se),i(pe,H),nt()}function _i(pe,n){let j=E(n,"markdown",8);const Ce={codespan:bs},h=se(()=>(a(j()),o(()=>j().replace(/`?#[0-9a-fA-F]{3,6}`?/g,f=>f.startsWith("`")?f:`\`${f}\``))));Xi(pe,{get markdown(){return e(h)},get renderers(){return Ce}})}function Mi(pe,n){return`${pe}:${n}`}const pi=(pe,n)=>{let j=null,Ce=null,h=null,f=!1;function O(){h&&cancelAnimationFrame(h),h=requestAnimationFrame(()=>{const{path:M,onCollapseStateChange:K}=n;if(f)return void(h=null);const D=Array.from(document.querySelectorAll(`[data-description-id^="${M}:"]`));let k=!1;for(const y of D)if(y!==pe&&le(pe,y)){k=!0;break}k&&(f=!0),K&&K(k),h=null})}function le(M,K){const D=M.getBoundingClientRect(),k=K.getBoundingClientRect();return!(D.bottom<=k.top||k.bottom<=D.top)}function H(){ce(),j=new MutationObserver(()=>{O()});const M=pe.closest(".descriptions")||document.body;j.observe(M,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&(Ce=new ResizeObserver(()=>{O()}),Ce.observe(pe)),window.addEventListener("resize",O),window.addEventListener("scroll",O)}function ce(){j&&(j.disconnect(),j=null),Ce&&(Ce.disconnect(),Ce=null),h&&(cancelAnimationFrame(h),h=null),window.removeEventListener("resize",O),window.removeEventListener("scroll",O)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{H(),O()}):requestAnimationFrame(()=>{H(),O()}),{update:M=>{n=M,f=!1,O()},destroy:ce}};var As=v('<div role="region" aria-label="Code diff description"><div class="c-diff-description__content svelte-wweiw1"><!></div> <div class="c-diff-description__truncated-content svelte-wweiw1"><!> <div class="c-diff-description__expand-hint svelte-wweiw1">hover to expand</div></div></div>'),ks=v('<div class="toggle-button svelte-1r29xbx"><!></div> <div class="descriptions svelte-1r29xbx"></div>',1),zs=v('<div><div class="editor-container svelte-1r29xbx"></div> <!></div>');function Fs(pe,n){it(n,!1);const[j,Ce]=Wt(),h=()=>xt(Q,"$monaco",j),f=()=>xt(_e,"$descriptionPositions",j),O=Vi();let le=E(n,"originalCode",8,""),H=E(n,"modifiedCode",8,""),ce=E(n,"path",8),M=E(n,"descriptions",24,()=>[]),K=E(n,"lineOffset",8,0),D=E(n,"extraPrefixLines",24,()=>[]),k=E(n,"extraSuffixLines",24,()=>[]),y=E(n,"theme",8),S=E(n,"areDescriptionsVisible",12,!0),I=E(n,"isNewFile",8,!1),ne=E(n,"isDeletedFile",8,!1);const Q=ls.getContext().monaco;let J,u=c(),z=c(),L=c(),m=[],ue=c();const F=os();let b,r=ri(0),U=c(I()?20*H().split(`
`).length+40:100);const me=h()?h().languages.getLanguages().map(C=>C.id):[];function Ee(C,P){var G,ie,ae;if(P){const s=(G=P.split(".").pop())==null?void 0:G.toLowerCase();if(s){const A=(ae=(ie=h())==null?void 0:ie.languages.getLanguages().find($=>{var _;return(_=$.extensions)==null?void 0:_.includes("."+s)}))==null?void 0:ae.id;if(A&&me.includes(A))return A}}return"plaintext"}const _e=ri({});let ke=null;function Me(){if(!e(u))return;m=m.filter(G=>(G.dispose(),!1));const C=e(u).getOriginalEditor(),P=e(u).getModifiedEditor();m.push(C.onDidScrollChange(()=>{bi(r,C.getScrollTop())}),P.onDidScrollChange(()=>{bi(r,P.getScrollTop())}))}function oe(){if(!e(u)||!e(ue))return;const C=e(u).getOriginalEditor(),P=e(u).getModifiedEditor();m.push(P.onDidContentSizeChange(()=>F.requestLayout()),C.onDidContentSizeChange(()=>F.requestLayout()),e(u).onDidUpdateDiff(()=>F.requestLayout()),P.onDidChangeHiddenAreas(()=>F.requestLayout()),C.onDidChangeHiddenAreas(()=>F.requestLayout()),P.onDidLayoutChange(()=>F.requestLayout()),C.onDidLayoutChange(()=>F.requestLayout()),P.onDidFocusEditorWidget(()=>{Ye(!0)}),C.onDidFocusEditorWidget(()=>{Ye(!0)}),P.onDidBlurEditorWidget(()=>{Ye(!1)}),C.onDidBlurEditorWidget(()=>{Ye(!1)}),P.onDidChangeModelContent(()=>{var s;Te=!0,De=Date.now();const G=((s=e(L))==null?void 0:s.getValue())||"";if(G===H())return;const ie=G.replace(D().join(""),"").replace(k().join(""),"");O("codeChange",{modifiedCode:ie});const ae=setTimeout(()=>{Te=!1},500);m.push({dispose:()=>clearTimeout(ae)})})),function(){!e(ue)||!e(u)||(ke&&clearTimeout(ke),ke=setTimeout(()=>{if(!e(ue).__hasClickListener){const G=ie=>{const ae=ie.target;ae&&(ae.closest('[title="Show Unchanged Region"]')||ae.closest('[title="Hide Unchanged Region"]'))&&Ve()};e(ue).addEventListener("click",G),zt(ue,e(ue).__hasClickListener=!0),m.push({dispose:()=>{e(ue).removeEventListener("click",G)}})}e(u)&&m.push(e(u).onDidUpdateDiff(()=>{Ve()}))},300))}()}Ci(()=>{var C,P,G;(C=e(u))==null||C.dispose(),(P=e(z))==null||P.dispose(),J==null||J.dispose(),(G=e(L))==null||G.dispose(),m.forEach(ie=>ie.dispose()),ke&&clearTimeout(ke),b==null||b()});let ge=null;function Ve(){ge&&clearTimeout(ge),ge=setTimeout(()=>{F.requestLayout(),ge=null},100),ge&&m.push({dispose:()=>{ge&&(clearTimeout(ge),ge=null)}})}function Ue(C,P,G,ie=[],ae=[]){var $;if(!h())return void console.error("Monaco not loaded. Diff view cannot be updated.");J==null||J.dispose(),($=e(L))==null||$.dispose(),P=P||"",G=G||"";const s=ie.join(""),A=ae.join("");if(P=I()?G.split(`
`).map(()=>" ").join(`
`):s+P+A,G=s+G+A,J=h().editor.createModel(P,void 0,C!==void 0?h().Uri.parse("file://"+C+`#${crypto.randomUUID()}`):void 0),ne()&&(G=G.split(`
`).map(()=>" ").join(`
`)),t(L,h().editor.createModel(G,void 0,C!==void 0?h().Uri.parse("file://"+C+`#${crypto.randomUUID()}`):void 0)),e(u)){e(u).setModel({original:J,modified:e(L)});const _=e(u).getOriginalEditor();_&&_.updateOptions({lineNumbers:"off"}),Me(),ke&&clearTimeout(ke),ke=setTimeout(()=>{oe(),ke=null},300)}}ei(()=>{if(h())if(I()){t(z,h().editor.create(e(ue),{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:y(),scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:ie=>`${K()-D().length+ie}`}));const C=Ee(H(),ce());t(L,h().editor.createModel(H(),C,ce()!==void 0?h().Uri.parse("file://"+ce()+`#${crypto.randomUUID()}`):void 0)),e(z).setModel(e(L)),m.push(e(z).onDidChangeModelContent(()=>{var s;Te=!0,De=Date.now();const ie=((s=e(L))==null?void 0:s.getValue())||"";if(ie===H())return;O("codeChange",{modifiedCode:ie});const ae=setTimeout(()=>{Te=!1},500);m.push({dispose:()=>clearTimeout(ae)})})),m.push(e(z).onDidFocusEditorWidget(()=>{var ie;(ie=e(z))==null||ie.updateOptions({scrollbar:{handleMouseWheel:!0}})}),e(z).onDidBlurEditorWidget(()=>{var ie;(ie=e(z))==null||ie.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const P=e(z).getContentHeight();t(U,Math.max(P,60));const G=setTimeout(()=>{var ie;(ie=e(z))==null||ie.layout()},0);m.push({dispose:()=>clearTimeout(G)})}else t(u,h().editor.createDiffEditor(e(ue),{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:y(),scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:C=>`${K()-D().length+C}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),b&&b(),b=F.registerEditor({editor:e(u),updateHeight:Ge,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),Ue(ce(),le(),H(),D(),k()),Me(),oe(),ke&&clearTimeout(ke),ke=setTimeout(()=>{F.requestLayout(),ke=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let Te=!1,De=0;function ze(C,P=!0){return e(u)?(P?e(u).getModifiedEditor():e(u).getOriginalEditor()).getTopForLineNumber(C):18*C}function Ge(){if(!e(u))return;const C=e(u).getModel(),P=C==null?void 0:C.original,G=C==null?void 0:C.modified;if(!P||!G)return;const ie=e(u).getOriginalEditor(),ae=e(u).getModifiedEditor(),s=e(u).getLineChanges()||[];let A;if(s.length===0){const $=ie.getContentHeight(),_=ae.getContentHeight();A=Math.max(100,$,_)}else{let $=0,_=0;for(const x of s)x.originalEndLineNumber>0&&($=Math.max($,x.originalEndLineNumber)),x.modifiedEndLineNumber>0&&(_=Math.max(_,x.modifiedEndLineNumber));$=Math.min($+3,P.getLineCount()),_=Math.min(_+3,G.getLineCount());const Z=ie.getTopForLineNumber($),g=ae.getTopForLineNumber(_);A=Math.max(Z,g)+60}t(U,Math.min(A,2e4)),e(u).layout(),Be()}function Ye(C){if(!e(u))return;const P=e(u).getOriginalEditor(),G=e(u).getModifiedEditor();P.updateOptions({scrollbar:{handleMouseWheel:C}}),G.updateOptions({scrollbar:{handleMouseWheel:C}})}function He(C){if(!e(u))return e(z)?e(z).getTopForLineNumber(C.range.start+1):0;const P=e(u).getModel(),G=P==null?void 0:P.original,ie=P==null?void 0:P.modified;if(!G||!ie)return 0;const ae=ze(C.range.start+1,!1),s=ze(C.range.start+1,!0);return ae&&!s?ae:!ae&&s?s:Math.min(ae,s)}function Be(){if(!e(u)&&!e(z)||M().length===0)return;const C={};M().forEach((P,G)=>{C[G]=He(P)}),function(P,G=50){const ie=Object.keys(P).sort((ae,s)=>P[Number(ae)]-P[Number(s)]);for(let ae=0;ae<ie.length-1;ae++){const s=Number(ie[ae]),A=P[s];P[s+1]-A<G&&(P[Number(ie[ae+1])]=A+G)}}(C),_e.set(C)}const at=crypto.randomUUID();N(()=>(a(H()),a(I()),e(z),e(L),a(ce()),h(),e(u),a(le()),a(D()),a(k())),()=>{if(C=H(),!(Te||Date.now()-De<1e3||e(L)&&e(L).getValue()===D().join("")+C+k().join("")))if(I()&&e(z)){if(e(L))e(L).setValue(H());else{const P=Ee(H(),ce());h()&&t(L,h().editor.createModel(H(),P,ce()!==void 0?h().Uri.parse("file://"+ce()+`#${crypto.randomUUID()}`):void 0)),e(L)&&e(z).setModel(e(L))}t(U,20*H().split(`
`).length+40),e(z).layout()}else!I()&&e(u)&&(Ue(ce(),le(),H(),D(),k()),F.requestLayout());var C}),N(()=>(e(u),e(z),a(M())),()=>{(e(u)||e(z))&&M().length>0&&Be()}),N(()=>(a(I()),a(H()),e(z)),()=>{if(I()&&H()&&e(z)){const C=e(z).getContentHeight();t(U,Math.max(C,60)),e(z).layout()}}),Mt(),st();var Xe=zs();let ct;var Oe=l(Xe);di(Oe,C=>t(ue,C),()=>e(ue));var be=q(Oe,2),We=C=>{var P=ks(),G=we(P),ie=l(G);yi(ie,{variant:"ghost",color:"neutral",size:1,$$events:{click:()=>S(!S())},children:(s,A)=>{var $=Ft(),_=we($),Z=x=>{gi(x,{icon:"x"})},g=x=>{gi(x,{icon:"book"})};V(_,x=>{S()?x(Z):x(g,!1)}),i(s,$)},$$slots:{default:!0}});var ae=q(G,2);dt(ae,5,M,mt,(s,A,$)=>{const _=se(()=>(f(),e(A),o(()=>f()[$]||He(e(A)))));(function(Z,g){it(g,!1);const x=c();let d=E(g,"description",8),T=E(g,"position",8),p=E(g,"fileId",8),W=E(g,"index",8),Y=c(!1),ee=c(!1),w=c(),X=c(0);const R=vs($e=>{t(Y,$e)},100);function re($e){const Pe=document.createElement("canvas").getContext("2d");return Pe?Pe.measureText($e).width:8*$e.length}function te(){if(e(w)){const $e=e(w).getBoundingClientRect();t(X,$e.width-128)}}let ve=null;ei(()=>{e(w)&&typeof ResizeObserver<"u"&&(ve=new ResizeObserver(()=>{te()}),ve.observe(e(w)),te())}),Ci(()=>{ve&&ve.disconnect()}),N(()=>e(w),()=>{e(w)&&te()}),N(()=>(a(d()),e(X)),()=>{t(x,(()=>{const $e=d().text.split(`
`)[0].split(" ");let Pe="";if(e(X)<=0)for(const vt of $e){const lt=Pe+(Pe?" ":"")+vt;if(lt.length>30)break;Pe=lt}else for(const vt of $e){const lt=Pe+(Pe?" ":"")+vt;if(re(lt+"...")>e(X))break;Pe=lt}return Pe+"..."})())}),Mt(),st();var fe=As();let B;var Ae=l(fe);_i(l(Ae),{get markdown(){return a(d()),o(()=>d().text)}});var Re=q(Ae,2);_i(l(Re),{get markdown(){return e(x)}}),di(fe,$e=>t(w,$e),()=>e(w)),cs(fe,($e,Pe)=>pi==null?void 0:pi($e,Pe),()=>({path:p(),onCollapseStateChange:$e=>t(ee,$e)})),$i(()=>ti("mouseenter",fe,$e=>{R.cancel(),t(Y,!0),$e.stopPropagation()})),$i(()=>ti("mouseleave",fe,$e=>{R(!1),$e.stopPropagation()})),ye(($e,Pe)=>{B=Ht(fe,1,"c-diff-description svelte-wweiw1",null,B,$e),Xt(fe,`top: ${T()??""}px;`),Qe(fe,"data-description-id",Pe)},[()=>({"c-diff-description__collapsed":e(ee)&&!e(Y),"c-diff-description__hovered":e(Y)}),()=>(a(Mi),a(p()),a(W()),o(()=>Mi(p(),W())))],se),i(Z,fe),nt()})(s,{get description(){return e(A)},get position(){return e(_)},get fileId(){return at},index:$})}),ye(()=>Xt(ae,`transform: translateY(${-xt(r,"$scrollY",j)}px)`)),i(C,P)};V(be,C=>{a(M()),o(()=>M().length>0)&&C(We)}),ye(C=>{ct=Ht(Xe,1,"monaco-diff-container svelte-1r29xbx",null,ct,C),Xt(Oe,`height: ${e(U)??""}px`)},[()=>({"monaco-diff-container-with-descriptions":M().length>0&&S()})],se),i(pe,Xe),nt(),Ce()}const Pi=Symbol("focusedPath");function Ni(){return Ut(Pi)}function wi(pe){return`file-diff-${Vt(pe)}`}var xs=v('<!> <img class="image-preview svelte-1536g7w"/>',1),Ms=v("<!> <!>",1),Ls=v("<!> ",1),qs=v('<!> <img class="image-preview image-preview--previous svelte-1536g7w"/>',1),Es=v('<!> <img class="image-preview svelte-1536g7w"/> <!>',1),Ds=v('<div class="image-container svelte-1536g7w"><!></div>'),Ps=v("<!> No text preview available.",1),Ns=v('<div class="binary-file-message svelte-1536g7w"><!></div>'),Os=v('<div class="too-large-message svelte-1536g7w"><!></div>'),js=v('<div class="changes svelte-1536g7w"><!></div>'),Ss=v('<span class="c-directory svelte-1536g7w"> </span>'),Ts=v('<span class="new-file-badge svelte-1536g7w">New File</span>'),Zs=v('<span class="additions svelte-1536g7w"><!></span>'),Vs=v('<span class="deletions svelte-1536g7w"><!></span>'),Hs=v('<div class="changes-indicator svelte-1536g7w"><!> <!></div>'),Rs=v('<div class="applied svelte-1536g7w"><!></div>'),Is=v('<div class="applied__icon svelte-1536g7w"><!></div>'),Us=v("<!> <!>",1),Bs=v('<div slot="header" class="header svelte-1536g7w"><!> <div class="c-path svelte-1536g7w"><!> <!></div> <!> <!> <!></div>'),Ws=v("<div><!></div>");function Oi(pe,n){it(n,!1);const[j,Ce]=Wt(),h=()=>xt(Hi,"$themeStore",j),f=c(),O=c(),le=c(),H=c(),ce=c(),M=c(),K=c(),D=c(),k=c(),y=c(),S=c(),I=c(),ne=c(),Q=c(),J=c(),u=c(),z=c(),L=c(),m=c(),ue=c(),F=c();let b=E(n,"path",8),r=E(n,"change",12),U=E(n,"descriptions",24,()=>[]),me=E(n,"areDescriptionsVisible",12,!0),Ee=E(n,"isExpandedDefault",8),_e=E(n,"isCollapsed",28,()=>!Ee()),ke=E(n,"isApplying",8),Me=E(n,"hasApplied",8),oe=E(n,"onApplyChanges",24,()=>{}),ge=E(n,"onCodeChange",24,()=>{}),Ve=E(n,"onOpenFile",24,()=>{}),Ue=E(n,"isAgentFromDifferentRepo",8,!1);const Te=Ni(),De=Ut(Bt.key);let ze=c(r().modifiedCode);function Ge(be){var We;t(ze,be.detail.modifiedCode),(We=ge())==null||We(e(ze))}function Ye(){var be,We;De.reportApplyChangesEvent(),r(r().modifiedCode=e(ze),!0),(be=ge())==null||be(e(ze)),(We=oe())==null||We()}let He=c(e(m));function Be(){t(He,`Open ${e(m)??"file"}`)}async function at(){Ve()&&(t(He,"Opening file..."),await Ve()()?Be():(t(He,"Failed to open file. Does the file exist?"),setTimeout(()=>{Be()},2e3)))}ei(()=>{Be()}),N(()=>a(r()),()=>{t(ze,r().modifiedCode)}),N(()=>a(r()),()=>{t(f,es(r().diff))}),N(()=>e(f),()=>{t(O,e(f).additions)}),N(()=>e(f),()=>{t(le,e(f).deletions)}),N(()=>a(r()),()=>{t(H,ts(r()))}),N(()=>a(r()),()=>{t(ce,is(r()))}),N(()=>a(b()),()=>{t(M,hs(b()))}),N(()=>a(b()),()=>{t(K,Qt(b()))}),N(()=>a(b()),()=>{t(D,us(b()))}),N(()=>a(r()),()=>{var be;t(k,((be=r().originalCode)==null?void 0:be.length)||0)}),N(()=>e(ze),()=>{var be;t(y,((be=e(ze))==null?void 0:be.length)||0)}),N(()=>e(k),()=>{t(S,zi(e(k)))}),N(()=>e(y),()=>{t(I,zi(e(y)))}),N(()=>(e(ze),a(r())),()=>{t(ne,!e(ze)&&!!r().originalCode)}),N(()=>(e(ze),a(r())),()=>{t(Q,!!e(ze)&&!r().originalCode)}),N(()=>e(M),()=>{t(J,e(M))}),N(()=>(e(M),e(D)),()=>{t(u,!e(M)&&e(D))}),N(()=>(e(M),e(D),e(I),e(ne),e(S),e(Q)),()=>{t(z,!e(M)&&!e(D)&&(e(I)||e(ne)&&e(S)||e(Q)&&e(I)))}),N(()=>h(),()=>{var be,We;t(L,rs((be=h())==null?void 0:be.category,(We=h())==null?void 0:We.intensity))}),N(()=>a(b()),()=>{t(m,ps(b()))}),N(()=>(a(ke()),a(Ue())),()=>{t(ue,ke()||Ue())}),N(()=>(a(ke()),a(Me()),a(Ue())),()=>{t(F,ke()?"Applying changes...":Me()?"Reapply changes to local file":Ue()?"Cannot apply changes from a different repository locally":"Apply changes to local file")}),Mt(),st();var Xe=Ws();let ct;var Oe=l(Xe);Bi(Oe,{stickyHeader:!0,get collapsed(){return _e()},set collapsed(be){_e(be)},children:(be,We)=>{var C=js(),P=l(C),G=ae=>{var s=Ds(),A=l(s),$=Z=>{var g=Ms(),x=we(g);qe(x,{class:"image-info-text",children:(p,W)=>{var Y=de();ye(ee=>Se(Y,`Image deleted: ${ee??""}`),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(p,Y)},$$slots:{default:!0}});var d=q(x,2),T=p=>{var W=xs(),Y=we(W);qe(Y,{class:"image-info-text",children:(w,X)=>{var R=de("Previous version:");i(w,R)},$$slots:{default:!0}});var ee=q(Y,2);ye((w,X,R)=>{Qe(ee,"src",`data:${w??""};base64,${X??""}`),Qe(ee,"alt",`Original ${R??""}`)},[()=>(a(Qt),a(b()),o(()=>Qt(b()))),()=>(a(r()),o(()=>btoa(r().originalCode))),()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(p,W)};V(d,p=>{a(r()),o(()=>r().originalCode)&&p(T)}),i(Z,g)},_=(Z,g)=>{var x=d=>{var T=Es(),p=we(T);qe(p,{class:"image-info-text",children:(w,X)=>{var R=Ls(),re=we(R),te=B=>{var Ae=de("New image added");i(B,Ae)},ve=B=>{var Ae=de("Image modified");i(B,Ae)};V(re,B=>{e(H)||e(Q)?B(te):B(ve,!1)});var fe=q(re);ye(B=>Se(fe,`: ${B??""}`),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(w,R)},$$slots:{default:!0}});var W=q(p,2),Y=q(W,2),ee=w=>{var X=qs(),R=we(X);qe(R,{class:"image-info-text",children:(te,ve)=>{var fe=de("Previous version:");i(te,fe)},$$slots:{default:!0}});var re=q(R,2);ye((te,ve,fe)=>{Qe(re,"src",`data:${te??""};base64,${ve??""}`),Qe(re,"alt",`Original ${fe??""}`)},[()=>(a(Qt),a(b()),o(()=>Qt(b()))),()=>(a(r()),o(()=>btoa(r().originalCode))),()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(w,X)};V(Y,w=>{a(r()),e(ze),e(H),o(()=>r().originalCode&&e(ze)!==r().originalCode&&!e(H))&&w(ee)}),ye((w,X)=>{Qe(W,"src",`data:${e(K)??""};base64,${w??""}`),Qe(W,"alt",`Current ${X??""}`)},[()=>(e(ze),o(()=>btoa(e(ze)))),()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(d,T)};V(Z,d=>{e(ze)&&d(x)},g)};V(A,Z=>{e(ne)?Z($):Z(_,!1)}),i(ae,s)},ie=(ae,s)=>{var A=_=>{var Z=Ns(),g=l(Z);qe(g,{children:(x,d)=>{var T=Ps(),p=we(T),W=ee=>{var w=de();ye(X=>Se(w,`Binary file added: ${X??""}.`),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(ee,w)},Y=(ee,w)=>{var X=re=>{var te=de();ye(ve=>Se(te,`Binary file deleted: ${ve??""}.`),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(re,te)},R=re=>{var te=de();ye(ve=>Se(te,`Binary file modified: ${ve??""}.`),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(re,te)};V(ee,re=>{e(ne)?re(X):re(R,!1)},w)};V(p,ee=>{e(H)||e(Q)?ee(W):ee(Y,!1)}),i(x,T)},$$slots:{default:!0}}),i(_,Z)},$=(_,Z)=>{var g=d=>{var T=Os(),p=l(T);qe(p,{size:1,children:(W,Y)=>{var ee=de();ye(w=>Se(ee,`File "${w??""}" is too large to display a diff (size: ${(e(ne)?e(k):e(y))??""} bytes, max: ${gs} bytes).`),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(W,ee)},$$slots:{default:!0}}),i(d,T)},x=d=>{Fs(d,{get path(){return b()},get originalCode(){return a(r()),o(()=>r().originalCode)},get modifiedCode(){return e(ze)},get theme(){return e(L)},get descriptions(){return U()},get isNewFile(){return e(H)},get isDeletedFile(){return e(ce)},get areDescriptionsVisible(){return me()},set areDescriptionsVisible(T){me(T)},$$events:{codeChange:Ge},$$legacy:!0})};V(_,d=>{e(z)?d(g):d(x,!1)},Z)};V(ae,_=>{e(u)?_(A):_($,!1)},s)};V(P,ae=>{e(J)?ae(G):ae(ie,!1)}),i(be,C)},$$slots:{default:!0,header:(be,We)=>{var C=Bs(),P=l(C);Wi(P,{});var G=q(P,2),ie=l(G);const ae=se(()=>(a(Ot),o(()=>[Ot.Hover])));jt(ie,{get content(){return e(He)},get triggerOn(){return e(ae)},delayDurationMs:300,children:(p,W)=>{_t(p,{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$events:{click:at},children:(Y,ee)=>{var w=de();ye(X=>Se(w,X),[()=>(a(Ze),e(m),o(()=>Ze(e(m))))],se),i(Y,w)},$$slots:{default:!0}})},$$slots:{default:!0}});var s=q(ie,2),A=p=>{var W=Ss(),Y=l(W);ye(ee=>Se(Y,ee),[()=>(a(ai),e(m),o(()=>ai(e(m))))],se),i(p,W)};V(s,p=>{a(ai),e(m),o(()=>ai(e(m)))&&p(A)});var $=q(G,2),_=p=>{var W=Ts();i(p,W)},Z=p=>{var W=Hs(),Y=l(W),ee=R=>{var re=Zs(),te=l(re);qe(te,{size:1,children:(ve,fe)=>{var B=de();ye(()=>Se(B,`+${e(O)??""}`)),i(ve,B)},$$slots:{default:!0}}),i(R,re)};V(Y,R=>{e(O)>0&&R(ee)});var w=q(Y,2),X=R=>{var re=Vs(),te=l(re);qe(te,{size:1,children:(ve,fe)=>{var B=de();ye(()=>Se(B,`-${e(le)??""}`)),i(ve,B)},$$slots:{default:!0}}),i(R,re)};V(w,R=>{e(le)>0&&R(X)}),i(p,W)};V($,p=>{e(H)?p(_):p(Z,!1)});var g=q($,2);const x=se(()=>(a(Ot),o(()=>[Ot.Hover])));jt(g,{get content(){return e(F)},get triggerOn(){return e(x)},delayDurationMs:300,children:(p,W)=>{_t(p,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(ue)},$$events:{click:Ye},children:(Y,ee)=>{var w=Us(),X=we(w),R=B=>{var Ae=de("Applied");i(B,Ae)},re=B=>{var Ae=de("Apply");i(B,Ae)};V(X,B=>{Me()?B(R):B(re,!1)});var te=q(X,2),ve=B=>{var Ae=Rs(),Re=l(Ae);mi(Re,{iconName:"check"}),i(B,Ae)},fe=B=>{var Ae=Is(),Re=l(Ae);ci(Re,{}),i(B,Ae)};V(te,B=>{Me()?B(ve):B(fe,!1)}),i(Y,w)},$$slots:{default:!0}})},$$slots:{default:!0}});var d=q(g,2),T=p=>{const W=se(()=>(a(Ot),o(()=>[Ot.Hover])));jt(p,{get content(){return e(He)},get triggerOn(){return e(W)},delayDurationMs:300,children:(Y,ee)=>{yi(Y,{size:1,variant:"ghost",color:"neutral",$$events:{click:at},children:(w,X)=>{Ei(w,{})},$$slots:{default:!0}})},$$slots:{default:!0}})};V(d,p=>{Me()&&p(T)}),i(be,C)}},$$legacy:!0}),ye((be,We)=>{ct=Ht(Xe,1,"c svelte-1536g7w",null,ct,be),Qe(Xe,"id",We)},[()=>({focused:xt(Te,"$focusedPath",j)===b()}),()=>(a(wi),a(b()),o(()=>wi(b())))],se),i(pe,Xe),nt(),Ce()}var Js=v('<span class="full-path-text svelte-qnxoj"> </span>'),Gs=v('<div class="tree-node__children svelte-qnxoj" role="group"></div>'),Ys=v('<div class="tree-node svelte-qnxoj"><div role="treeitem" tabindex="0"><div class="tree-node__indent svelte-qnxoj"></div> <div class="tree-node__icon-container svelte-qnxoj"><!></div> <span><!></span></div> <!></div>');function ji(pe,n){it(n,!1);const[j,Ce]=Wt(),h=()=>xt(le,"$focusedPath",j);let f=E(n,"node",12),O=E(n,"indentLevel",8,0);const le=Ni();function H(){f().isFile?le.set(f().path):f(f().isExpanded=!f().isExpanded,!0)}st();var ce=Ys(),M=l(ce);let K;var D=l(M),k=q(D,2),y=l(k),S=L=>{const m=se(()=>(a(f()),o(()=>f().isExpanded?"chevron-down":"chevron-right")));gi(L,{get icon(){return e(m)}})},I=L=>{Di(L,{get filename(){return a(f()),o(()=>f().name)}})};V(y,L=>{a(f()),o(()=>!f().isFile)?L(S):L(I,!1)});var ne=q(k,2);let Q;var J=l(ne);qe(J,{size:1,children:(L,m)=>{var ue=Js(),F=l(ue);ye(()=>Se(F,(a(f()),o(()=>f().displayName||f().name)))),i(L,ue)},$$slots:{default:!0}});var u=q(M,2),z=L=>{var m=Gs();dt(m,5,()=>(a(f()),o(()=>Array.from(f().children.values()).sort((ue,F)=>ue.isFile===F.isFile?ue.name.localeCompare(F.name):ue.isFile?1:-1))),mt,(ue,F)=>{var b=Ft(),r=we(b);const U=se(()=>O()+1);ji(r,{get node(){return e(F)},get indentLevel(){return e(U)}}),i(ue,b)}),i(L,m)};V(u,L=>{a(f()),o(()=>!f().isFile&&f().isExpanded&&f().children.size>0)&&L(z)}),ye((L,m)=>{K=Ht(M,1,"tree-node__content svelte-qnxoj",null,K,L),Qe(M,"aria-selected",(a(f()),h(),o(()=>f().path===h()))),Qe(M,"aria-expanded",(a(f()),o(()=>f().isFile?void 0:f().isExpanded))),Xt(D,`width: ${6*O()}px`),Q=Ht(ne,1,"tree-node__label svelte-qnxoj",null,Q,m),Qe(ne,"title",(a(f()),o(()=>f().displayName||f().name)))},[()=>({selected:f().path===h(),"collapsed-folder":f().displayName&&!f().isFile}),()=>({"full-path":f().displayName})],se),ti("click",M,H),ti("keydown",M,L=>L.key==="Enter"&&H()),i(pe,ce),nt(),Ce()}var Ks=v('<div class="tree-view__loading svelte-1tnd9l7"><div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div></div>'),Qs=v('<div class="tree-view__empty svelte-1tnd9l7"><!></div>'),Xs=v('<div class="tree-view svelte-1tnd9l7"><div class="tree-view__content svelte-1tnd9l7" role="tree" aria-label="Changed Files"><!></div></div>');function Si(pe,n){it(n,!1);const j=c();let Ce=E(n,"changedFiles",24,()=>[]),h=E(n,"isLoading",8,!1);function f(D){const k={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return D.forEach(y=>{const S=y.change_type===ms.deleted?y.old_path:y.new_path;S&&function(I,ne){const Q=ne.split("/");let J=I;for(let u=0;u<Q.length;u++){const z=Q[u],L=u===Q.length-1,m=Q.slice(0,u+1).join("/");J.children.has(z)||J.children.set(z,{name:z,path:m,isFile:L,children:new Map,isExpanded:!0}),J=J.children.get(z)}}(k,S)}),function(y){if(!y.isFile){if(y.path===""){const S=Array.from(y.children.values()).filter(I=>!I.isFile);for(const I of S)O(I,!0);return}O(y)}}(k),k}function O(D,k=!1){if(D.isFile)return;let y="";k&&(y=function(Q){let J=Q.path.split("/"),u=Q;for(;;){const z=Array.from(u.children.values()).filter(m=>!m.isFile),L=Array.from(u.children.values()).filter(m=>m.isFile);if(z.length!==1||L.length!==0)break;u=z[0],J.push(u.name)}return J.join("/")}(D));const S=Array.from(D.children.values()).filter(Q=>!Q.isFile);for(const Q of S)O(Q);const I=Array.from(D.children.values()).filter(Q=>!Q.isFile),ne=Array.from(D.children.values()).filter(Q=>Q.isFile);if(I.length===1&&ne.length===0){const Q=I[0],J=Q.name;if(k){D.displayName=y||`${D.name}/${J}`;for(const[u,z]of Q.children.entries()){const L=`${u}`;D.children.set(L,z)}D.children.delete(J)}else{D.displayName?Q.displayName=`${D.displayName}/${J}`:Q.displayName=`${D.name}/${J}`;for(const[u,z]of Q.children.entries()){const L=`${J}/${u}`;D.children.set(L,z)}D.children.delete(J)}}}N(()=>a(Ce()),()=>{t(j,f(Ce()))}),Mt(),st();var le=Xs(),H=l(le),ce=l(H),M=D=>{var k=Ks();i(D,k)},K=(D,k)=>{var y=I=>{var ne=Qs(),Q=l(ne);qe(Q,{size:1,color:"neutral",children:(J,u)=>{var z=de("No changed files");i(J,z)},$$slots:{default:!0}}),i(I,ne)},S=I=>{var ne=Ft(),Q=we(ne);dt(Q,1,()=>(e(j),o(()=>Array.from(e(j).children.values()).sort((J,u)=>J.isFile===u.isFile?J.name.localeCompare(u.name):J.isFile?1:-1))),mt,(J,u)=>{ji(J,{get node(){return e(u)},indentLevel:0})}),i(I,ne)};V(D,I=>{e(j),o(()=>e(j).children.size===0)?I(y):I(S,!1)},k)};V(ce,D=>{h()?D(M):D(K,!1)}),i(pe,le),nt()}var en=v('<!> <div class="c-edits-list-controls__icon svelte-6iqvaj"><!></div>',1),tn=v("<div><!></div>"),sn=v('<div class="c-edits-list-header svelte-6iqvaj"><div class="c-edits-list-controls svelte-6iqvaj"><!></div></div> <div class="c-edits-list svelte-6iqvaj"><div class="c-edits-section svelte-6iqvaj"></div></div>',1),nn=v('<div class="c-edits-list c-edits-list--empty svelte-6iqvaj"><!></div>'),an=v('<div class="c-edits-list-container svelte-6iqvaj"><div class="c-file-explorer__layout svelte-6iqvaj"><div class="c-file-explorer__tree svelte-6iqvaj"><div class="c-file-explorer__tree__header svelte-6iqvaj"><!> <!></div></div> <div class="c-file-explorer__details svelte-6iqvaj"><!></div></div></div>');function ln(pe,n){it(n,!1);const j=c(),Ce=c(),h=c(),f=c(),O=c();let le=E(n,"changedFiles",8),H=E(n,"onApplyChanges",8),ce=E(n,"onOpenFile",24,()=>{}),M=E(n,"pendingFiles",24,()=>[]),K=E(n,"appliedFiles",24,()=>[]),D=E(n,"isLoadingTreeView",8,!1),k=c({}),y=c(!1),S=c(!1);function I(){if(!H())return;const b=e(O).map(U=>U.qualifiedPathName.relPath);if(b.every(U=>K().includes(U)))return void t(S,!0);const r=b.filter(U=>!K().includes(U)&&!M().includes(U));r.length!==0&&(t(y,!0),t(S,!1),r.forEach(U=>{const me=e(O).find(Ee=>Ee.qualifiedPathName.relPath===U);if(me){const Ee=e(k)[U]||me.newContents;H()(U,me.oldContents,Ee)}}))}N(()=>a(le()),()=>{t(j,JSON.stringify(le()))}),N(()=>a(K()),()=>{t(Ce,JSON.stringify(K()))}),N(()=>a(M()),()=>{t(h,JSON.stringify(M()))}),N(()=>e(j),()=>{e(j)&&(t(k,{}),t(y,!1),t(S,!1))}),N(()=>(a(le()),e(k)),()=>{t(O,le().map(b=>{const r=b.new_path||b.old_path,U=b.old_contents||"",me=b.new_contents||"",Ee=ss.generateDiff(b.old_path,b.new_path,U,me),_e=function(ke,Me){const oe=ui("oldFile","newFile",ke,Me,"","",{context:3}),ge=Qi(oe);let Ve=0,Ue=0,Te=[];for(const De of ge)for(const ze of De.hunks)for(const Ge of ze.lines){const Ye=Ge.startsWith("+"),He=Ge.startsWith("-");Ye&&Ve++,He&&Ue++,Te.push({value:Ge,added:Ye,removed:He})}return{totalAddedLines:Ve,totalRemovedLines:Ue,changes:Te,diff:oe}}(U,me);return e(k)[r]||zt(k,e(k)[r]=me),{qualifiedPathName:{rootPath:"",relPath:r},lineChanges:_e,oldContents:U,newContents:me,diff:Ee}}))}),N(()=>(e(j),e(Ce),e(h),e(O),a(K()),a(M())),()=>{t(f,(()=>{if(e(j)&&e(Ce)&&e(h)){const b=e(O).map(r=>r.qualifiedPathName.relPath);return b.length!==0&&b.some(r=>!K().includes(r)&&!M().includes(r))}return!1})())}),N(()=>(e(y),e(O),a(K()),a(M())),()=>{if(e(y)){const b=e(O).map(r=>r.qualifiedPathName.relPath);b.filter(r=>!K().includes(r)&&!M().includes(r)).length===0&&b.every(r=>K().includes(r)||M().includes(r))&&M().length===0&&K().length>0&&(t(y,!1),t(S,!0))}}),N(()=>(e(O),e(y),e(Ce),a(K()),e(S)),()=>{if(e(O).length>0&&!e(y)&&e(Ce)){const b=e(O).map(r=>r.qualifiedPathName.relPath);if(b.length>0){const r=b.every(U=>K().includes(U));r&&K().length>0?t(S,!0):!r&&e(S)&&t(S,!1)}}}),Mt(),st();var ne=an(),Q=l(ne),J=l(Q),u=l(J),z=l(u);qe(z,{size:1,class:"c-file-explorer__tree__header__label",children:(b,r)=>{var U=de("Changed files");i(b,U)},$$slots:{default:!0}}),Si(q(z,2),{get changedFiles(){return le()},get isLoading(){return D()}});var L=q(J,2),m=l(L),ue=b=>{var r=sn(),U=we(r),me=l(U),Ee=l(me),_e=oe=>{const ge=se(()=>(e(y),e(S),a(M()),e(f),o(()=>e(y)||e(S)||M().length>0||!e(f))));_t(oe,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(ge)},$$events:{click:I},children:(Ve,Ue)=>{var Te=en(),De=we(Te),ze=Be=>{var at=de("Applying...");i(Be,at)},Ge=(Be,at)=>{var Xe=Oe=>{var be=de("All applied");i(Oe,be)},ct=Oe=>{var be=de("Apply all");i(Oe,be)};V(Be,Oe=>{e(S)?Oe(Xe):Oe(ct,!1)},at)};V(De,Be=>{e(y)?Be(ze):Be(Ge,!1)});var Ye=q(De,2),He=l(Ye);ci(He,{}),i(Ve,Te)},$$slots:{default:!0}})};V(Ee,oe=>{e(O),o(()=>e(O).length>0)&&oe(_e)});var ke=q(U,2),Me=l(ke);dt(Me,5,()=>e(O),oe=>oe.qualifiedPathName.relPath,(oe,ge)=>{var Ve=tn(),Ue=l(Ve);const Te=se(()=>(a(M()),e(ge),o(()=>M().includes(e(ge).qualifiedPathName.relPath)))),De=se(()=>(a(K()),e(ge),o(()=>K().includes(e(ge).qualifiedPathName.relPath)))),ze=se(()=>ce()?()=>ce()(e(ge).qualifiedPathName.relPath):void 0);Oi(Ue,{get path(){return e(ge),o(()=>e(ge).qualifiedPathName.relPath)},get change(){return e(ge),o(()=>e(ge).diff)},get isApplying(){return e(Te)},get hasApplied(){return e(De)},onCodeChange:Ge=>{(function(Ye,He){zt(k,e(k)[Ye]=He)})(e(ge).qualifiedPathName.relPath,Ge)},onApplyChanges:()=>{const Ge=e(k)[e(ge).qualifiedPathName.relPath]||e(ge).newContents;H()(e(ge).qualifiedPathName.relPath,e(ge).oldContents,Ge)},get onOpenFile(){return e(ze)},isExpandedDefault:!0}),Yi(3,Ve,()=>Ki),i(oe,Ve)}),i(b,r)},F=b=>{var r=nn(),U=l(r);qe(U,{size:1,color:"neutral",children:(me,Ee)=>{var _e=de("No changes to show");i(me,_e)},$$slots:{default:!0}}),i(b,r)};V(m,b=>{e(O),o(()=>e(O).length>0)?b(ue):b(F,!1)}),i(pe,ne),nt()}var on=Li('<path fill-rule="evenodd" clip-rule="evenodd"></path>'),rn=Li('<svg width="14" viewBox="0 0 20 20" fill="currentColor" class="svelte-10h4f31"><!></svg>'),dn=v('<div class="c-skeleton-diff__controls svelte-1eiztmz"><div class="c-skeleton-diff__button svelte-1eiztmz"></div></div>'),cn=v('<div class="c-skeleton-diff__changes-item svelte-1eiztmz"><div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div></div>'),vn=v('<div class="c-skeleton-diff__subsection svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__changes svelte-1eiztmz"></div></div>'),fn=v('<div class="c-skeleton-diff__section svelte-1eiztmz"><div class="c-skeleton-diff__header svelte-1eiztmz"><div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div></div> <!></div> <!></div>'),pn=v('<div class="c-skeleton-diff svelte-1eiztmz"></div>'),hn=v("<!> <!>",1),un=v('<div class="c-conflicts-card__file svelte-1bce35u"><!> <!></div>'),gn=v('<div class="c-conflicts-card__header svelte-1bce35u"><div class="c-conflicts-card__icon svelte-1bce35u"><!></div> <span class="c-conflicts-card__title svelte-1bce35u"> </span></div> <div class="c-conflicts-card__description svelte-1bce35u"><!></div> <!>',1),mn=v('<div class="c-conflicts-card"><!></div>'),_n=v(`There are unstaged changes in your working directory. Please commit your changes or we will
      run <!> to stash your changes before applying changes from the remote agent.`,1),wn=v('<div class="c-unstaged-changes-modal__body svelte-9eyy34"><!> <!></div>'),yn=v('<div class="c-unstaged-changes-modal__stash-button-loading svelte-9eyy34"><!></div>'),Cn=v("<!> <span>Stash & Apply Locally</span>",1),$n=v('<div class="c-unstaged-changes-modal__footer svelte-9eyy34" slot="footer"><!> <!></div>'),bn=v('<div class="c-diff-view__error svelte-ibi4q5"><!> <!> <!></div>'),An=v('<div class="c-diff-view__empty svelte-ibi4q5"><!></div>'),kn=v("<!> <!>",1),zn=v("<!> <!>",1),Fn=v('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),xn=v("Applied <!>",1),Mn=v('<div class="c-diff-view__applied svelte-ibi4q5"><!></div>'),Ln=v('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),qn=v('<div class="c-diff-view__controls svelte-ibi4q5"><!></div> <!>',1),En=v('<div class="c-diff-view__skeleton-title svelte-ibi4q5"></div>'),Dn=v('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div> <div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>',1),Pn=v("<!> Collapse All",1),Nn=v("<!> Expand All",1),On=v('<div class="c-diff-view__applying svelte-ibi4q5"><!> <!></div>'),jn=v('<div class="c-diff-view__applied svelte-ibi4q5"><!> <!></div>'),Sn=v('Apply All <div class="c-diff-view__controls__icon svelte-ibi4q5"><!></div>',1),Tn=v('<div class="c-diff-view__controls svelte-ibi4q5"><!> <!></div>'),Zn=v('<div class="c-diff-view__skeleton-text svelte-ibi4q5"></div>'),Vn=v('<div class="c-diff-view__warning svelte-ibi4q5"><!> </div>'),Hn=v('<div class="c-diff-view__changes-item svelte-ibi4q5"><!></div>'),Rn=v('<div class="c-diff-view__subsection svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><div class="c-diff-view__icon svelte-ibi4q5"><!></div> <h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <!></div></div> <div class="c-diff-view__changes svelte-ibi4q5"></div></div>'),In=v('<div class="c-diff-view__section svelte-ibi4q5"><div class="c-diff-view__header svelte-ibi4q5"><div class="c-diff-view__content svelte-ibi4q5"><h5 class="c-diff-view__title svelte-ibi4q5"><!></h5> <div class="c-diff-view__description svelte-ibi4q5"><!></div></div> <!></div> <!> <!></div>'),Un=v('<div class="c-diff-view__layout svelte-ibi4q5"><div class="c-diff-view__tree svelte-ibi4q5"><div class="c-diff-view__tree__header svelte-ibi4q5"><!> <!> <!> <!></div></div> <div class="c-diff-view__explanation svelte-ibi4q5"><!></div></div>'),Bn=v('<div class="c-diff-view svelte-ibi4q5"><!> <!></div> <!>',1);function Wn(pe,n){it(n,!1);const[j,Ce]=Wt(),h=()=>xt(Te,"$diffViewFilesMap",j),f=c(),O=c(),le=c(),H=c(),ce=c(),M=c(),K=c(),D=c();let k=E(n,"changedFiles",8),y=E(n,"agentLabel",24,()=>{}),S=E(n,"latestUserPrompt",24,()=>{}),I=E(n,"onApplyChanges",24,()=>{}),ne=E(n,"onOpenFile",24,()=>{}),Q=E(n,"onRenderBackup",24,()=>{}),J=E(n,"preloadedExplanation",24,()=>{}),u=E(n,"isAgentFromDifferentRepo",8,!1),z=E(n,"conflictFiles",24,()=>new Set);const L=Ut(Bt.key);let m="",ue=c(!1),F=c([]),b=c([]),r=c(!1),U=c(!1),me=c(null),Ee=c(!0),_e=c({}),ke=c([]),Me=c(!1),oe=c(!1),ge=c(!0),Ve=c(new Set),Ue=c(!1);const Te=ri({});let De=c({});function ze(){const s=li(e(F)),A=Object.values(e(_e)).some(Boolean);t(Ee,A),Array.from(s).forEach($=>{zt(_e,e(_e)[$]=!e(Ee))})}async function Ge(s,A,$){if(I())return Te.update(_=>(_[s]="pending",_)),new Promise(_=>{var Z;(Z=I())==null||Z(s,A,$).then(()=>{Te.update(g=>(g[s]="applied",g)),_()})})}async function Ye(){const s=await L.canApplyChanges();s.canApply?He():s.hasUnstagedChanges&&t(Ue,!0)}function He(){if(!I())return;L.reportApplyChangesEvent(),t(Me,!0),t(oe,!1);const{filesToApply:s,areAllPathsApplied:A}=Fi(e(F),k(),e(De));A||s.length===0?t(oe,A):_s(s,Ge).then(()=>{t(Me,!1),t(oe,!0)})}function Be(s){const A={title:"Changed Files",description:`${s.length} files were changed`,sections:[]},$=[],_=[],Z=[];return s.forEach(g=>{g.old_path?g.new_path?_.push(g):Z.push(g):$.push(g)}),$.length>0&&A.sections.push(at("Added files","feature",$)),_.length>0&&A.sections.push(at("Modified files","fix",_)),Z.length>0&&A.sections.push(at("Deleted files","chore",Z)),[A]}function at(s,A,$){const _=[];return $.forEach(Z=>{const g=Z.new_path||Z.old_path,x=Z.old_contents||"",d=Z.new_contents||"",T=Z.old_path?Z.old_path:"",p=ui(T,Z.new_path||"/dev/null",x,d,"","",{context:3}),W=`${Vt(g)}-${Vt(x+d)}`;_.push({id:W,path:g,diff:p,originalCode:x,modifiedCode:d})}),{title:s,descriptions:[],type:A,changes:_}}async function Xe(){if(!e(ue))return;if(t(r,!0),t(U,!1),t(me,null),t(b,[]),t(F,[]),e(ce))return void t(r,!1);const s=102400;let A=0;if(k().forEach($=>{var _,Z;A+=(((_=$.old_contents)==null?void 0:_.length)||0)+(((Z=$.new_contents)==null?void 0:Z.length)||0)}),k().length>12||A>512e3){try{t(F,Be(k()))}catch($){console.error("Failed to create simple explanation:",$),t(me,"Failed to create explanation for large changes.")}t(r,!1)}else try{const $=new ws(g=>qi.postMessage(g)),_=new Map,Z=k().map(g=>{const x=g.new_path||g.old_path,d=g.old_contents||"",T=g.new_contents||"",p=`${Vt(x)}-${Vt(d+T)}`;return _.set(p,{old_path:g.old_path,new_path:g.new_path,old_contents:d,new_contents:T,change_type:g.change_type}),{id:p,old_path:g.old_path,new_path:g.new_path,change_type:g.change_type}});try{const g=Z.length===1;let x=[];g?x=Z.map(d=>({path:d.new_path||d.old_path,changes:[{id:d.id,path:d.new_path||d.old_path,diff:`File: ${d.new_path||d.old_path}
Change type: ${d.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):x=(await $.send({type:"get-diff-group-changes-request",data:{changedFiles:Z,changesById:!0,apikey:m}},3e4)).data.groupedChanges,t(b,x.map(d=>({path:d.path,changes:d.changes.map(T=>{if(T.id&&_.has(T.id)){const p=_.get(T.id);let W=T.diff;return W&&!W.startsWith("File:")||(W=ui(p.old_path||"",p.new_path||"",p.old_contents||"",p.new_contents||"")),{...T,diff:W,old_path:p.old_path,new_path:p.new_path,old_contents:p.old_contents,new_contents:p.new_contents,change_type:p.change_type,originalCode:p.old_contents||"",modifiedCode:p.new_contents||""}}return T})})))}catch(g){console.error("Failed to group changes with LLM, falling back to simple grouping:",g);try{const x=Z.map(d=>{if(d.id&&_.has(d.id)){const T=_.get(d.id);return{...d,old_path:T.old_path,new_path:T.new_path,old_contents:T.old_contents||"",new_contents:T.new_contents||"",change_type:T.change_type}}return d});t(F,Be(x)),t(b,e(F)[0].sections.map(d=>({path:d.title,changes:d.changes}))),t(U,!1)}catch(x){console.error("Failed to create simple explanation:",x),t(me,"Failed to group changes. Please try again.")}}if(t(r,!1),!e(b)||e(b).length===0)throw new Error("Failed to group changes");if(!e(F)||e(F).length===0){t(F,function(x){const d={title:"Loading...",description:"",sections:[]};return x.forEach(T=>{const p=T.changes.map(Y=>{if(Y.id)return Y;const ee=Vt(Y.path),w=Vt(Y.originalCode+Y.modifiedCode);return{...Y,id:`${ee}-${w}`}}),W={title:T.path,descriptions:[],type:"other",changes:p};d.sections.push(W)}),[d]}(e(b)));const g=e(F)[0].sections.map(x=>({path:x.title,changes:x.changes.map(d=>{var Y,ee,w;const T=((Y=d.originalCode)==null?void 0:Y.length)||0,p=((ee=d.modifiedCode)==null?void 0:ee.length)||0,W=((w=d.diff)==null?void 0:w.length)||0;return T>s||p>s||W>s?{id:d.id,path:d.path,diff:`File: ${d.path}
Content too large to include in explanation request (${Math.max(T,p,W)} bytes)`,originalCode:T>s?`[File content too large: ${T} bytes]`:d.originalCode,modifiedCode:p>s?`[File content too large: ${p} bytes]`:d.modifiedCode}:{id:d.id,path:d.path,diff:d.diff,originalCode:d.originalCode,modifiedCode:d.modifiedCode}})}));t(U,!0);try{const{explanation:x,error:d}=await L.getDescriptions(g,m);if(d==="Token limit exceeded")return t(F,Be(k())),t(r,!1),void t(U,!1);x&&x.length>0&&x.forEach((T,p)=>{T.sections&&T.sections.forEach((W,Y)=>{W.changes&&W.changes.forEach(ee=>{const w=e(F)[p];if(w&&w.sections){const X=w.sections[Y];if(X&&X.changes){const R=X.changes.find(re=>re.id===ee.id);R&&(ee.originalCode=R.originalCode,ee.modifiedCode=R.modifiedCode,ee.diff=R.diff)}}})})}),t(F,x)}catch(x){console.error("Failed to get descriptions, using skeleton explanation:",x)}}e(F).length===0&&t(me,"Failed to generate explanation.")}catch($){console.error("Failed to get explanation:",$),t(me,$ instanceof Error?$.message:"An error occurred while generating the explanation.")}finally{t(r,!1),t(U,!1)}}ei(()=>{const s=localStorage.getItem("anthropic_apikey");s&&(m=s),t(ue,!0)});let ct=c(""),Oe=c("Apply all changes locally");N(()=>(a(k()),h()),()=>{k()&&Te.set(k().reduce((s,A)=>{const $=A.new_path||A.old_path;return s[$]=h()[$]??"none",s},{}))}),N(()=>e(_e),()=>{t(f,Object.values(e(_e)).some(Boolean))}),N(()=>a(k()),()=>{t(M,JSON.stringify(k()))}),N(()=>(e(ue),e(M),e(ct),a(J())),()=>{e(ue)&&e(M)&&e(M)!==e(ct)&&(t(ct,e(M)),J()&&J().length>0?(t(F,J()),t(r,!1),t(U,!1)):Xe(),t(Me,!1),t(oe,!1),t(De,{}))}),N(()=>(e(F),e(De)),()=>{e(F)&&e(F).length>0&&e(F).flatMap(s=>s.sections||[]).flatMap(s=>s.changes).forEach(s=>{e(De)[s.path]||zt(De,e(De)[s.path]=s.modifiedCode)})}),N(()=>e(F),()=>{t(O,JSON.stringify(e(F)))}),N(()=>(e(F),e(_e),e(Ee)),()=>{if(e(F)&&e(F).length>0){const s=li(e(F));Array.from(s).forEach(_=>{e(_e)[_]===void 0&&zt(_e,e(_e)[_]=!e(Ee))});const A=Object.keys(e(_e)).filter(_=>e(_e)[_]),$=Array.from(s);$.length>0&&t(Ee,!$.some(_=>A.includes(_)))}}),N(()=>(e(O),h(),e(F)),()=>{t(le,(()=>{if(e(O)&&h()){const s=li(e(F));return s.size!==0&&Array.from(s).some(A=>h()[A]!=="applied")}return!1})())}),N(()=>h(),()=>{t(oe,Object.keys(h()).every(s=>h()[s]==="applied"))}),N(()=>h(),()=>{t(H,Object.keys(h()).filter(s=>h()[s]==="pending"))}),N(()=>(e(F),a(k()),e(De)),()=>{(async function(s,A,$){const{filesToApply:_}=Fi(s,A,$),Z=new Set;for(const g of _)(await L.previewApplyChanges(g.path,g.originalCode,g.newCode)).hasConflicts&&Z.add(g.path);t(Ve,Z)})(e(F),k(),e(De))}),N(()=>a(k()),()=>{t(ce,k().length===0)}),N(()=>(e(O),e(oe),e(F),h()),()=>{if(e(O)&&e(oe)){const s=li(e(F));Array.from(s).every(A=>h()[A]==="applied")||t(oe,!1)}}),N(()=>(e(oe),a(z())),()=>{t(K,e(oe)&&z().size>0)}),N(()=>(a(u()),e(Me),e(oe),e(H),e(le)),()=>{t(D,u()||e(Me)||e(oe)||e(H).length>0||!e(le))}),N(()=>(e(D),a(u()),e(Me),e(K),e(oe),e(H),e(le)),()=>{e(D)?u()?t(Oe,"Cannot apply changes from a different repository locally"):e(Me)?t(Oe,"Applying changes..."):e(K)?t(Oe,"All changes applied, but conflicts need to be resolved manually"):e(oe)?t(Oe,"All changes applied"):e(H).length>0?t(Oe,"Waiting for changes to apply"):e(le)||t(Oe,"No changes to apply"):t(Oe,"Apply all changes locally")}),Mt(),st();var be=Bn(),We=we(be),C=l(We),P=s=>{var A=bn(),$=l(A);xi($,{});var _=q($),Z=q(_);_t(Z,{variant:"ghost",size:1,$$events:{click:Xe},children:(d,T)=>{var p=de("Retry");i(d,p)},$$slots:{default:!0}});var g=q(Z,2),x=d=>{_t(d,{variant:"ghost",size:1,$$events:{click(...T){var p;(p=Q())==null||p.apply(this,T)}},children:(T,p)=>{var W=de("Render as list");i(T,W)},$$slots:{default:!0}})};V(g,d=>{Q()&&d(x)}),ye(()=>Se(_,` ${e(me)??""} `)),i(s,A)};V(C,s=>{e(me)&&s(P)});var G=q(C,2),ie=s=>{var A=An(),$=l(A);qe($,{size:2,color:"secondary",children:(_,Z)=>{var g=de("No files changed");i(_,g)},$$slots:{default:!0}}),i(s,A)},ae=s=>{var A=Un(),$=l(A),_=l($),Z=l(_),g=w=>{var X=kn(),R=we(X);qe(R,{size:1,class:"c-diff-view__tree__header__label",children:(te,ve)=>{var fe=de("Changes from agent");i(te,fe)},$$slots:{default:!0}});var re=q(R,2);qe(re,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(te,ve)=>{var fe=de();ye(()=>Se(fe,y())),i(te,fe)},$$slots:{default:!0}}),i(w,X)};V(Z,w=>{y()&&S()!==y()&&w(g)});var x=q(Z,2),d=w=>{var X=zn(),R=we(X);qe(R,{size:1,class:"c-diff-view__tree__header__label",children:(te,ve)=>{var fe=de("Last user prompt");i(te,fe)},$$slots:{default:!0}});var re=q(R,2);qe(re,{size:1,weight:"medium",class:"c-diff-view__tree__header__title",children:(te,ve)=>{var fe=de();ye(()=>Se(fe,S())),i(te,fe)},$$slots:{default:!0}}),i(w,X)};V(x,w=>{S()&&w(d)});var T=q(x,2);qe(T,{size:1,class:"c-diff-view__tree__header__label",children:(w,X)=>{var R=de("Changed files");i(w,R)},$$slots:{default:!0}}),Si(q(T,2),{get changedFiles(){return k()}});var p=q($,2),W=l(p),Y=w=>{var X=qn(),R=we(X),re=l(R);const te=se(()=>e(Me)?"Applying changes...":e(oe)?"All changes applied":e(le)?"Apply all changes":"No changes to apply");jt(re,{get content(){return e(te)},children:(ve,fe)=>{const B=se(()=>(e(Me),e(oe),e(H),e(le),o(()=>e(Me)||e(oe)||e(H).length>0||!e(le))));_t(ve,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(B)},$$events:{click:Ye},children:(Ae,Re)=>{var $e=Ft(),Pe=we($e),vt=ht=>{var Lt=Fn(),qt=l(Lt);oi(qt,{size:1,useCurrentColor:!0});var ft=q(qt,2);qe(ft,{size:2,children:(et,wt)=>{var Et=de("Applying...");i(et,Et)},$$slots:{default:!0}}),i(ht,Lt)},lt=(ht,Lt)=>{var qt=et=>{var wt=Mn(),Et=l(wt);qe(Et,{size:2,children:(Dt,Jt)=>{var Rt=xn(),ii=q(we(Rt));mi(ii,{iconName:"check"}),i(Dt,Rt)},$$slots:{default:!0}}),i(et,wt)},ft=et=>{var wt=Ln(),Et=q(we(wt)),Dt=l(Et);ci(Dt,{}),i(et,wt)};V(ht,et=>{e(oe)?et(qt):et(ft,!1)},Lt)};V(Pe,ht=>{e(Me)?ht(vt):ht(lt,!1)}),i(Ae,$e)},$$slots:{default:!0}})},$$slots:{default:!0}}),function(ve,fe){let B=E(fe,"count",8,2);var Ae=pn();dt(Ae,5,()=>(a(B()),o(()=>Array(B()))),mt,(Re,$e,Pe)=>{var vt=fn(),lt=l(vt),ht=q(l(lt),2),Lt=ft=>{var et=dn();i(ft,et)};V(ht,ft=>{Pe===0&&ft(Lt)});var qt=q(lt,2);dt(qt,0,()=>Array(2),mt,(ft,et,wt,Et)=>{var Dt=vn(),Jt=q(l(Dt),2);dt(Jt,4,()=>Array(2),mt,(Rt,ii,Le,xe)=>{var yt=cn();i(Rt,yt)}),i(ft,Dt)}),i(Re,vt)}),i(ve,Ae)}(q(R,2),{count:2}),i(w,X)},ee=(w,X)=>{var R=re=>{var te=Ft(),ve=we(te);dt(ve,1,()=>e(F),mt,(fe,B,Ae)=>{var Re=In();Qe(Re,"id",`section-${Ae}`);var $e=l(Re),Pe=l($e),vt=l(Pe),lt=l(vt),ht=Le=>{var xe=En();i(Le,xe)},Lt=Le=>{var xe=de();ye(()=>Se(xe,(e(B),o(()=>e(B).title)))),i(Le,xe)};V(lt,Le=>{e(U),e(B),o(()=>e(U)&&e(B).title==="Loading...")?Le(ht):Le(Lt,!1)});var qt=q(vt,2),ft=l(qt),et=Le=>{var xe=Dn();i(Le,xe)},wt=Le=>{_i(Le,{get markdown(){return e(B),o(()=>e(B).description)}})};V(ft,Le=>{e(U),e(B),o(()=>e(U)&&e(B).description==="")?Le(et):Le(wt,!1)});var Et=q(Pe,2),Dt=Le=>{var xe=Tn(),yt=l(xe);_t(yt,{variant:"ghost-block",color:"neutral",size:2,$$events:{click:ze},children:(gt,Gt)=>{var Ct=Ft(),Pt=we(Ct),Nt=pt=>{var ot=Pn(),Ke=we(ot);Ji(Ke,{}),i(pt,ot)},St=pt=>{var ot=Nn(),Ke=we(ot);as(Ke),i(pt,ot)};V(Pt,pt=>{e(f)?pt(St,!1):pt(Nt)}),i(gt,Ct)},$$slots:{default:!0}});var ut=q(yt,2);jt(ut,{get content(){return e(Oe)},children:(gt,Gt)=>{_t(gt,{variant:"ghost-block",color:"neutral",size:2,get disabled(){return e(D)},$$events:{click:Ye},children:(Ct,Pt)=>{var Nt=Ft(),St=we(Nt),pt=Ke=>{var $t=On(),Ne=l($t);oi(Ne,{size:1,useCurrentColor:!0});var he=q(Ne,2);qe(he,{size:2,children:(je,Je)=>{var tt=de("Applying...");i(je,tt)},$$slots:{default:!0}}),i(Ke,$t)},ot=(Ke,$t)=>{var Ne=je=>{var Je=jn(),tt=l(Je);qe(tt,{size:2,children:(Fe,Ie)=>{var rt=de("Applied");i(Fe,rt)},$$slots:{default:!0}});var bt=q(tt,2),Tt=Fe=>{ki(Fe,{slot:"rightIcon"})},Zt=Fe=>{mi(Fe,{iconName:"check"})};V(bt,Fe=>{e(K)?Fe(Tt):Fe(Zt,!1)}),i(je,Je)},he=je=>{var Je=Sn(),tt=q(we(Je)),bt=l(tt);ci(bt,{}),i(je,Je)};V(Ke,je=>{e(oe)?je(Ne):je(he,!1)},$t)};V(St,Ke=>{e(Me)?Ke(pt):Ke(ot,!1)}),i(Ct,Nt)},$$slots:{default:!0}})},$$slots:{default:!0}}),i(Le,xe)};V(Et,Le=>{Ae===0&&Le(Dt)});var Jt=q($e,2),Rt=Le=>{const xe=se(()=>e(oe)?z():e(Ve));(function(yt,ut){it(ut,!1);let gt=E(ut,"files",8),Gt=E(ut,"hasAppliedAll",8),Ct=E(ut,"onOpenFile",24,()=>{});st();var Pt=mn(),Nt=l(Pt);fs(Nt,{includeBackground:!1,children:(St,pt)=>{var ot=gn(),Ke=we(ot),$t=l(Ke),Ne=l($t);ki(Ne,{});var he=q($t,2),je=l(he),Je=q(Ke,2),tt=l(Je),bt=Fe=>{var Ie=de("The following files have merge conflicts that need to be resolved manually.");i(Fe,Ie)},Tt=Fe=>{var Ie=de(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`);i(Fe,Ie)};V(tt,Fe=>{Gt()?Fe(bt):Fe(Tt,!1)});var Zt=q(Je,2);dt(Zt,1,gt,mt,(Fe,Ie)=>{var rt=un(),At=l(rt);jt(At,{get content(){return e(Ie)},nested:!0,children:(It,fi)=>{var kt=hn(),Kt=we(kt);Di(Kt,{get filename(){return e(Ie)}});var si=q(Kt,2);ys(si,{get filepath(){return e(Ie)}}),i(It,kt)},$$slots:{default:!0}});var Yt=q(At,2);jt(Yt,{content:"Open file",children:(It,fi)=>{yi(It,{size:1,variant:"ghost-block",color:"neutral",$$events:{click:()=>{var kt;return(kt=Ct())==null?void 0:kt(e(Ie))}},children:(kt,Kt)=>{Ei(kt,{})},$$slots:{default:!0}})},$$slots:{default:!0}}),i(Fe,rt)}),ye(()=>Se(je,`Conflicts (${a(gt()),o(()=>gt().size)??""})`)),i(St,ot)},$$slots:{default:!0}}),i(yt,Pt),nt()})(Le,{get files(){return e(xe)},get hasAppliedAll(){return e(oe)},get onOpenFile(){return ne()}})};V(Jt,Le=>{e(oe),a(z()),e(Ve),o(()=>(e(oe)&&z().size>0||!e(oe)&&e(Ve).size>0)&&Ae===0)&&Le(Rt)});var ii=q(Jt,2);dt(ii,1,()=>(e(B),o(()=>e(B).sections||[])),mt,(Le,xe,yt)=>{var ut=Rn();Qe(ut,"id",`subsection-${Ae}-${yt}`);var gt=l(ut),Gt=l(gt),Ct=l(Gt);(function(Ne,he){it(he,!1);const je=c();let Je=E(he,"type",8);const tt={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};N(()=>a(Je()),()=>{t(je,tt[Je()]??tt.other)}),Mt(),st();const bt=se(()=>`This is a ${Je()} change`),Tt=se(()=>(a(Ot),o(()=>[Ot.Hover])));jt(Ne,{get content(){return e(bt)},get triggerOn(){return e(Tt)},children:(Zt,Fe)=>{var Ie=rn(),rt=l(Ie),At=Yt=>{var It=Ft(),fi=we(It);dt(fi,1,()=>(e(je),o(()=>e(je).paths)),mt,(kt,Kt)=>{var si=on();ye(()=>Qe(si,"d",e(Kt))),i(kt,si)}),i(Yt,It)};V(rt,Yt=>{e(je)&&Yt(At)}),i(Zt,Ie)},$$slots:{default:!0}}),nt()})(l(Ct),{get type(){return e(xe),o(()=>e(xe).type)}});var Pt=q(Ct,2),Nt=l(Pt),St=Ne=>{var he=Zn();i(Ne,he)},pt=Ne=>{var he=de();ye(()=>Se(he,(e(xe),o(()=>e(xe).title)))),i(Ne,he)};V(Nt,Ne=>{e(U),e(xe),o(()=>e(U)&&e(xe).descriptions.length===0)?Ne(St):Ne(pt,!1)});var ot=q(Pt,2),Ke=Ne=>{var he=Vn(),je=l(he);xi(je,{});var Je=q(je);ye(()=>Se(Je,` ${e(xe),o(()=>e(xe).warning)??""}`)),i(Ne,he)};V(ot,Ne=>{e(U),e(xe),o(()=>!e(U)&&e(xe).warning)&&Ne(Ke)});var $t=q(gt,2);dt($t,5,()=>(e(xe),o(()=>e(xe).changes)),Ne=>Ne.id,(Ne,he)=>{var je=Hn(),Je=l(je);const tt=se(()=>(e(_e),e(he),e(Ee),o(()=>e(_e)[e(he).path]!==void 0?!e(_e)[e(he).path]:e(Ee)))),bt=se(()=>(h(),e(he),o(()=>h()[e(he).path]==="pending"))),Tt=se(()=>(h(),e(he),o(()=>h()[e(he).path]==="applied"))),Zt=se(()=>ne()?()=>ne()(e(he).path):void 0);di(Oi(Je,{get path(){return e(he),o(()=>e(he).path)},get change(){return e(he)},get descriptions(){return e(xe),o(()=>e(xe).descriptions)},get isExpandedDefault(){return e(tt)},get isApplying(){return e(bt)},get hasApplied(){return e(Tt)},onCodeChange:Fe=>{(function(Ie,rt){zt(De,e(De)[Ie]=rt)})(e(he).path,Fe)},onApplyChanges:()=>{Ge(e(he).path,e(he).originalCode,e(he).modifiedCode)},get onOpenFile(){return e(Zt)},get isAgentFromDifferentRepo(){return u()},get isCollapsed(){return e(_e)[e(he).path]},set isCollapsed(Fe){zt(_e,e(_e)[e(he).path]=Fe)},get areDescriptionsVisible(){return e(ge)},set areDescriptionsVisible(Fe){t(ge,Fe)},$$legacy:!0}),(Fe,Ie,rt,At)=>zt(ke,e(ke)[100*Ie+10*rt+At.path.length%10]=Fe),(Fe,Ie,rt)=>{var At;return(At=e(ke))==null?void 0:At[100*Fe+10*Ie+rt.path.length%10]},()=>[Ae,yt,e(he)]),i(Ne,je)}),i(Le,ut)}),i(fe,Re)}),i(re,te)};V(w,re=>{e(F),o(()=>e(F)&&e(F).length>0)&&re(R)},X)};V(W,w=>{e(r),e(b),o(()=>e(r)&&e(b).length===0)?w(Y):w(ee,!1)}),i(s,A)};V(G,s=>{e(ce)?s(ie):s(ae,!1)}),function(s,A){it(A,!1);let $=E(A,"showModal",12,!1),_=E(A,"applyAllChanges",8);const Z=Ut(Bt.key);let g=c(void 0),x=c(!1);async function d(){if(t(x,!0),!await Z.stashUnstagedChanges())return t(g,"Failed to stash changes. Please manually stash or commit your unstaged changes."),void t(x,!1);await new Promise(p=>setTimeout(p,1500)),t(g,void 0),$(!1),_()(),t(x,!1)}function T(){$(!1),t(g,void 0)}st(),Cs(s,{get show(){return $()},title:"Unstaged changes",$$events:{cancel:T},children:(p,W)=>{var Y=wn(),ee=l(Y);qe(ee,{children:(R,re)=>{var te=_n(),ve=q(we(te));ns(ve,{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}),i(R,te)},$$slots:{default:!0}});var w=q(ee,2),X=R=>{qe(R,{children:(re,te)=>{var ve=de();ye(()=>Se(ve,e(g))),i(re,ve)},$$slots:{default:!0}})};V(w,R=>{e(g)&&R(X)}),i(p,Y)},$$slots:{default:!0,footer:(p,W)=>{var Y=$n(),ee=l(Y);const w=se(()=>!!e(g)||e(x));_t(ee,{variant:"solid",color:"accent",get disabled(){return e(w)},$$events:{click:d},children:(R,re)=>{var te=Cn(),ve=we(te),fe=Re=>{var $e=yn(),Pe=l($e);oi(Pe,{size:1}),i(Re,$e)};V(ve,Re=>{e(x)&&Re(fe)});var B=q(ve,2);let Ae;ye(Re=>Ae=Ht(B,1,"c-unstaged-changes-modal__stash-button-text svelte-9eyy34",null,Ae,Re),[()=>({loading:e(x)})],se),i(R,te)},$$slots:{default:!0}});var X=q(ee,2);_t(X,{variant:"solid",color:"neutral",get disabled(){return e(x)},$$events:{click:T},children:(R,re)=>{var te=de("Abort");i(R,te)},$$slots:{default:!0}}),i(p,Y)}}}),nt()}(q(We,2),{applyAllChanges:He,get showModal(){return e(Ue)},set showModal(s){t(Ue,s)},$$legacy:!0}),i(pe,be),nt(),Ce()}var Jn=v('<div class="file-explorer-contents svelte-5tfpo4"><!></div>'),Gn=v('<div class="diff-page svelte-5tfpo4"><div class="file-explorer-main svelte-5tfpo4"><!></div></div>');function Yn(pe,n){it(n,!1);const[j,Ce]=Wt(),h=()=>xt(k,"$diffModel",j);let f=E(n,"changedFiles",24,()=>[]),O=E(n,"pendingFiles",24,()=>[]),le=E(n,"appliedFiles",24,()=>[]),H=E(n,"agentLabel",24,()=>{}),ce=E(n,"latestUserPrompt",24,()=>{}),M=E(n,"isAgentFromDifferentRepo",8,!1),K=c(new Set);const D=Ut(Bt.key),k=Ut(vi.key);let y=c("summary");const S=async(z,L,m)=>{const{success:ue,hasConflicts:F}=await D.applyChanges(z,L,m);ue&&F&&t(K,new Set([...e(K),z]))},I=z=>D.openFile(z);(function(z){z.subscribe(L=>{if(L){const m=document.getElementById(wi(L));m&&m.scrollIntoView({behavior:"smooth",block:"center"})}})})(function(z=null){const L=ri(z);return hi(Pi,L),L}(null)),st();var ne=Gn(),Q=l(ne),J=l(Q),u=z=>{var L=Jn(),m=l(L);Gi(m,()=>(h(),o(()=>h().opts)),ue=>{var F=Ft(),b=we(F),r=me=>{ln(me,{get changedFiles(){return f()},onApplyChanges:S,onOpenFile:I,get pendingFiles(){return O()},get appliedFiles(){return le()}})},U=me=>{const Ee=se(()=>(h(),o(()=>{var _e,ke;return(ke=(_e=h())==null?void 0:_e.opts)==null?void 0:ke.preloadedExplanation})));Wn(me,{get changedFiles(){return f()},onApplyChanges:S,onOpenFile:I,get agentLabel(){return H()},get latestUserPrompt(){return ce()},onRenderBackup:()=>{t(y,"changedFiles")},get preloadedExplanation(){return e(Ee)},get isAgentFromDifferentRepo(){return M()},get conflictFiles(){return e(K)}})};V(b,me=>{e(y)==="changedFiles"?me(r):me(U,!1)}),i(ue,F)}),i(z,L)};V(J,z=>{f()&&z(u)}),i(pe,ne),nt(),Ce()}var Kn=v('<div class="l-center svelte-ccste2"><!> <p>Loading diff view...</p></div>'),Qn=v('<div class="l-main svelte-ccste2"><!></div>');Ii(function(pe,n){it(n,!1);const[j,Ce]=Wt(),h=()=>xt(le,"$remoteAgentDiffModel",j),f=c();let O=new Ui(qi),le=new vi(O);O.registerConsumer(le);let H=new Bt(O);hi(Bt.key,H),hi(vi.key,le),ei(()=>(le.onPanelLoaded(),()=>{O.dispose()})),N(()=>h(),()=>{t(f,h().opts)}),Mt(),st(),ti("message",Ri,function(...ce){var M;(M=O.onMessageFromExtension)==null||M.apply(this,ce)}),ds.Root(pe,{children:(ce,M)=>{var K=Qn(),D=l(K),k=S=>{const I=se(()=>o(()=>H.applyingFilePaths||[])),ne=se(()=>o(()=>H.appliedFilePaths||[])),Q=se(()=>(e(f),o(()=>e(f).isAgentFromDifferentRepo||!1)));Yn(S,{get changedFiles(){return e(f),o(()=>e(f).changedFiles)},get agentLabel(){return e(f),o(()=>e(f).sessionSummary)},get latestUserPrompt(){return e(f),o(()=>e(f).userPrompt)},get pendingFiles(){return e(I)},get appliedFiles(){return e(ne)},get isAgentFromDifferentRepo(){return e(Q)}})},y=S=>{var I=Kn(),ne=l(I);oi(ne,{size:1}),i(S,I)};V(D,S=>{e(f)?S(k):S(y,!1)}),i(ce,K)},$$slots:{default:!0}}),nt(),Ce()},{target:document.getElementById("app")});
