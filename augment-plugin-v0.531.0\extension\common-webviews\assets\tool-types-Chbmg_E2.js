var t=(r=>(r.readFile="read-file",r.saveFile="save-file",r.editFile="edit-file",r.clarify="clarify",r.onboardingSubAgent="onboarding-sub-agent",r.launchProcess="launch-process",r.killProcess="kill-process",r.readProcess="read-process",r.writeProcess="write-process",r.listProcesses="list-processes",r.waitProcess="wait-process",r.openBrowser="open-browser",r.strReplaceEditor="str-replace-editor",r.remember="remember",r.diagnostics="diagnostics",r.webFetch="web-fetch",r.setupScript="setup-script",r.readTerminal="read-terminal",r.gitCommitRetrieval="git-commit-retrieval",r.memoryRetrieval="memory-retrieval",r.startWorkerAgent="start_worker_agent",r.readWorkerState="read_worker_state",r.waitForWorkerAgent="wait_for_worker_agent",r.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",r.stopWorkerAgent="stop_worker_agent",r.deleteWorkerAgent="delete_worker_agent",r.readWorkerAgentEdits="read_worker_agent_edits",r.applyWorkerAgentEdits="apply_worker_agent_edits",r.LocalSubAgent="local-sub-agent",r))(t||{}),o=(r=>(r.remoteToolHost="remoteToolHost",r.localToolHost="localToolHost",r.sidecarToolHost="sidecarToolHost",r.mcpHost="mcpHost",r))(o||{}),e=(r=>(r[r.ContentText=0]="ContentText",r[r.ContentImage=1]="ContentImage",r))(e||{}),a=(r=>(r[r.Unsafe=0]="Unsafe",r[r.Safe=1]="Safe",r[r.Check=2]="Check",r))(a||{}),s=(r=>(r[r.Unknown=0]="Unknown",r[r.WebSearch=1]="WebSearch",r[r.GitHubApi=8]="GitHubApi",r[r.Linear=12]="Linear",r[r.Jira=13]="Jira",r[r.Confluence=14]="Confluence",r[r.Notion=15]="Notion",r[r.Supabase=16]="Supabase",r[r.Glean=17]="Glean",r))(s||{});export{t as L,s as R,a as T,o as a,e as b};
