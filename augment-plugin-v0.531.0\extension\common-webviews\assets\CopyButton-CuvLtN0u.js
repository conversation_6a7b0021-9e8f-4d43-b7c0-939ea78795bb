import{G as w,aa as G,o as b,O as J,Y as M,b as s,x as O,z as y,m as Q,F as R,u as $,M as m,I as f,J as V,C as h,Q as Y,L as D}from"./legacy-YP6Kq8lu.js";import{p as a,s as E,a as x}from"./SpinnerAugment-Dpcl1cXc.js";import{C as H}from"./copy-ChvqXPeP.js";import{S as K}from"./TextAreaAugment-Bs79KMH3.js";import{s as U}from"./IconButtonAugment-CbpcmeFk.js";var W=w("<div><!></div>");function X(l,t){let n=a(t,"marginRight",3,0);var i=W(),u=b(i);U(u,()=>t.children??G),J(()=>M(i,1,`c-icon-size c-icon-size--size-${t.size??""} c-icon-size--margin-right-${n()??""}`,"svelte-18fg83")),s(l,i)}var Z=w('<span class="c-copy-button svelte-tq93gm"><!></span>');function rt(l,t){const n=E(t);O(t,!1);let i=a(t,"size",8,1),u=a(t,"iconSize",24,()=>{}),k=a(t,"variant",8,"ghost-block"),I=a(t,"color",8,"neutral"),c=a(t,"text",24,()=>{}),L=a(t,"tooltip",8,"Copy"),N=a(t,"stickyColor",8,!1),P=a(t,"onCopy",8,async()=>{if(c()!==void 0){y(p,!0);try{await Promise.all([navigator.clipboard.writeText(typeof c()=="string"?c():await c()()),(e=250,new Promise(o=>setTimeout(o,e,g)))])}finally{y(p,!1)}var e,g;return"success"}}),S=a(t,"tooltipNested",24,()=>{}),p=Q(!1);R();var v=Z(),T=b(v);const j=Y(()=>({neutral:L(),success:"Copied!"}));K(T,{get defaultColor(){return I()},get size(){return i()},get variant(){return k()},get loading(){return h(p)},get stickyColor(){return N()},get tooltip(){return h(j)},stateVariant:{success:"soft"},onClick:P(),icon:$(()=>!n.text),get tooltipNested(){return S()},children:(e,g)=>{var o=m(),d=f(o);x(d,t,"text",{},null),s(e,o)},$$slots:{default:!0,iconLeft:(e,g)=>{X(e,{get size(){return u()},children:(o,d)=>{var z=m(),q=f(z),A=r=>{var C=m(),F=f(C);x(F,t,"icon",{},null),s(r,C)},B=r=>{H(r,{})};V(q,r=>{$(()=>n.icon)?r(A):r(B,!1)}),s(o,z)},$$slots:{default:!0}})}}}),s(l,v),D()}export{rt as C,X as I};
